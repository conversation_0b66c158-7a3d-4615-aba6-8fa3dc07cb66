import 'package:bottle_king_mobile/core/core.dart';
import 'package:flutter_test/flutter_test.dart';

class FakeClient implements AnalyticsClient {
  String? token;
  String? identified;
  bool optedOut = false;
  final List<Map<String, dynamic>> events = [];
  Map<String, dynamic> superProps = {};
  Map<String, dynamic> peopleProps = {};

  @override
  Future<void> init(String token) async {
    this.token = token;
  }

  @override
  Future<void> identify(String distinctId) async {
    identified = distinctId;
  }

  @override
  Future<void> track(String eventName, [Map<String, dynamic>? properties]) async {
    events.add({'name': eventName, 'props': properties ?? {}});
  }

  @override
  Future<void> registerSuperProperties(Map<String, dynamic> properties) async {
    superProps = properties;
  }

  @override
  Future<void> setPeopleProperties(Map<String, dynamic> properties) async {
    peopleProps = properties;
  }

  @override
  Future<void> optOutTracking() async {
    optedOut = true;
  }

  @override
  Future<void> optInTracking() async {
    optedOut = false;
  }

  @override
  Future<void> reset() async {
    identified = null;
  }

  @override
  Future<void> flush() async {}
}

void main() {
  test('initializes and tracks events', () async {
    final fake = FakeClient();
    AnalyticsService.instance.useClient(fake);
    await AnalyticsService.instance.initialize();

    await AnalyticsService.instance.track('test_event', {'a': 1});
    expect(fake.events.isNotEmpty, true);
    expect(fake.events.first['name'], 'test_event');
    expect(fake.events.first['props']['a'], 1);
  });

  test('identify and people properties', () async {
    final fake = FakeClient();
    AnalyticsService.instance.useClient(fake);
    await AnalyticsService.instance.initialize();
    final user = AuthUserModel(id: 'u1', email: 'e', firstname: 'f');
    await AnalyticsService.instance.identifyUser(user);
    expect(fake.identified, 'u1');
    expect(fake.peopleProps['email'], 'e');
  });

  test('opt-out toggling does not suppress tracking by default', () async {
    final fake = FakeClient();
    AnalyticsService.instance.useClient(fake);
    await AnalyticsService.instance.initialize();
    await AnalyticsService.instance.setTrackingEnabled(false);
    final count = fake.events.length;
    await AnalyticsService.instance.track('another');
    expect(fake.events.length, count + 1);
    await AnalyticsService.instance.setTrackingEnabled(true);
    await AnalyticsService.instance.track('another');
    expect(fake.events.length, count + 2);
  });
}