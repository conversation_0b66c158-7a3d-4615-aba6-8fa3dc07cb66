Shorebird Code Push Integration

This app integrates Shorebird’s updater to allow:

- Get the currently installed patch version
- Check whether a new patch is available
- Download new patches

Usage

- Import via `package:bottle_king_mobile/core/core.dart` and use the service:

```dart
import 'package:bottle_king_mobile/core/core.dart';

final updater = ShorebirdUpdaterService.instance;

// Get installed patch number (or null)
final current = await updater.getInstalledPatchNumber();

// Check for update on the default (stable) track
final hasUpdate = await updater.isNewPatchAvailable();

// Optionally download the update (applies on next app restart)
final downloaded = await updater.downloadNewPatch();
```

Tracks (optional)

- Shorebird supports update tracks like `stable`, `beta`, `staging`, or custom names.
- Pass a track when checking or updating:

```dart
import 'package:shorebird_code_push/shorebird_code_push.dart';
final track = UpdateTrack.beta; // or UpdateTrack('my-custom-track');

final hasUpdate = await updater.isNewPatchAvailable(track: track);
if (hasUpdate) {
  await updater.downloadNewPatch(track: track);
}
```

Auto-check on startup

- A background auto-check is wired into `AppInitService.init`.
- It logs availability and downloads patches silently; patches apply on the next app restart.

Notes

- Restarting the app is required for a downloaded patch to take effect.
- Ensure your app is built with Shorebird and patches are published to tracks as needed.