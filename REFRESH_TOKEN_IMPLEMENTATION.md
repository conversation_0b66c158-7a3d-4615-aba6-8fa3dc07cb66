# Refresh Token Implementation

This document describes the comprehensive refresh token functionality implemented in the Bottle King Mobile app.

## Overview

The refresh token system provides automatic token renewal when access tokens expire, ensuring seamless user experience without requiring re-authentication. The implementation includes:

1. **Automatic Token Refresh**: Tokens are automatically refreshed when they expire
2. **Concurrent Request Handling**: Multiple simultaneous requests are handled gracefully during refresh
3. **Error Handling**: Proper handling of refresh failures with automatic logout
4. **State Management**: Clean state management with UI updates
5. **HTTP Interceptor**: Automatic retry of failed requests with new tokens

## Architecture

### Core Components

1. **TokenRefreshService** (`lib/core/services/token_refresh_service.dart`)
   - Independent service for token refresh operations
   - Handles concurrent refresh requests
   - Manages refresh state and queuing

2. **AuthVm** (`lib/core/providers/auth_vm.dart`)
   - Enhanced with refresh token functionality
   - Manages authentication state
   - Handles refresh failures with logout

3. **AppInterceptors** (`lib/core/services/dio/app_interceptor.dart`)
   - HTTP interceptor for automatic token refresh
   - Retries failed requests with new tokens
   - Handles 401/403 errors gracefully

4. **StorageService** (`lib/core/services/storage_service.dart`)
   - Secure token storage and retrieval
   - Token cleanup on logout

## Key Features

### 1. Automatic Token Refresh

When an API request receives a 401 Unauthorized response:
- The interceptor automatically attempts to refresh the access token
- If successful, the original request is retried with the new token
- If refresh fails, the user is automatically logged out

### 2. Concurrent Request Handling

- Multiple requests during token refresh are queued
- All pending requests receive the new token when refresh completes
- Prevents multiple simultaneous refresh attempts

### 3. Error Handling

- **Network Errors**: Graceful handling of network issues during refresh
- **Invalid Refresh Token**: Automatic logout when refresh token is expired/invalid
- **Refresh Endpoint Errors**: Proper error propagation and user notification

### 4. State Management

- Real-time UI updates during refresh operations
- Loading states for better user experience
- Clean state cleanup on logout

## Usage

### Basic Authentication Flow

```dart
// Login
final response = await ref.read(authVm.notifier).login(
  args: AuthArg(username: email, password: password)
);

// The system automatically handles token refresh for subsequent requests
final userResponse = await apiService.getWithAuth(url: "/customer");
```

### Manual Token Refresh

```dart
// Manually refresh token (rarely needed)
final authNotifier = ref.read(authVm.notifier);
final newToken = await authNotifier.refreshAccessToken();

if (newToken != null) {
  // Token refresh successful
} else {
  // Refresh failed, user will be logged out
}
```

### Testing Token Refresh

```dart
// Run comprehensive authentication tests
await AuthTestHelper.runAllTests();

// Test specific functionality
await AuthTestHelper.testTokenRefresh();
await AuthTestHelper.testAuthenticatedApiCall();
await AuthTestHelper.simulateTokenExpiry();
```

## API Endpoints

### Refresh Token Endpoint

**POST** `/auth/refresh`

**Request Body:**
```json
{
  "refreshToken": "your_refresh_token_here"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "new_access_token",
    "refreshToken": "new_refresh_token" // Optional
  }
}
```

## Configuration

### Environment Setup

Ensure your `.env` file contains the correct API base URL:

```env
DEV_URL=https://api.bottleking.ng
BASE_URL=https://api.bottleking.ng
```

### Initialization

The refresh token system is automatically initialized in `main.dart`:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppInitService().init();
  
  // Initialize API service with refresh token support
  ApiServiceInitializer.initialize();
  
  runApp(const ProviderScope(child: MyApp()));
}
```

## Security Considerations

1. **Token Storage**: Tokens are stored securely using Hive local storage
2. **Token Rotation**: Both access and refresh tokens can be rotated
3. **Automatic Cleanup**: Tokens are cleared on logout and refresh failure
4. **Request Isolation**: Refresh requests use a separate Dio instance to avoid loops

## Error Scenarios

### 1. Refresh Token Expired
- User is automatically logged out
- Redirected to welcome/login screen
- All stored tokens are cleared

### 2. Network Error During Refresh
- Original request fails with network error
- User can retry the operation
- Tokens remain valid for future attempts

### 3. Invalid Refresh Token
- User is logged out immediately
- All authentication state is cleared
- User must re-authenticate

## Best Practices

1. **Don't manually handle 401 errors** - The interceptor handles them automatically
2. **Use the provided API service methods** - They include automatic retry logic
3. **Monitor refresh state** - Use `authVm.isRefreshing` for UI feedback
4. **Test thoroughly** - Use `AuthTestHelper` for comprehensive testing

## Troubleshooting

### Common Issues

1. **Infinite refresh loops**: Ensure refresh endpoint doesn't use the main interceptor
2. **Token not updating**: Check that new tokens are properly stored
3. **UI not updating**: Ensure `reBuildUI()` is called after state changes

### Debug Logging

Enable detailed logging by checking console output with tag "Token Refresh":

```dart
printty("Token refresh successful", logName: "Token Refresh");
```

## Migration Notes

### From Previous Implementation

1. **Remove manual 401 handling** - The interceptor now handles this automatically
2. **Update logout calls** - Use the enhanced `AuthVm.logout()` method
3. **Remove custom retry logic** - The interceptor provides automatic retry

### Breaking Changes

- The `AppInterceptors` constructor no longer requires a container parameter
- Token refresh is now handled by `TokenRefreshService` instead of individual view models
- Logout behavior has been enhanced with proper state cleanup

## Testing

Run the authentication tests to verify the implementation:

```dart
// In your test environment or debug mode
await AuthTestHelper.runAllTests();
```

This will test:
- Authentication state
- Token refresh functionality
- Authenticated API calls
- Token expiry simulation

## Support

For issues or questions regarding the refresh token implementation, check:

1. Console logs with "Token Refresh" tag
2. Network requests in the developer tools
3. Token storage in local storage
4. Authentication state in the AuthVm provider
