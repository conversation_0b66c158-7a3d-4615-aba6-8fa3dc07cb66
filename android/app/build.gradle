plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new java.util.Properties()
def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new java.io.FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.bottlekingng.bottlekingng"
    compileSdk = 36
    ndkVersion = "26.1.10909125"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.bottlekingng.bottlekingng"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion flutter.minSdkVersion
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    
    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now,
            // so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
            signingConfig = signingConfigs.getByName("release")
        }
    }

     // START: Flavor Configuration
    flavorDimensions "environment"
    productFlavors {
        dev {
            dimension "environment"
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "BottleKing Dev"
        }
        staging {
            dimension "environment"
            applicationIdSuffix ".staging"
            resValue "string", "app_name", "BottleKing Staging"
        }
        prod {
            dimension "environment"
            resValue "string", "app_name", "BottleKing"
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}

// Configure Google Services to ignore package name mismatches
googleServices {
    disableVersionCheck = false
}

// Force all flavors to use the base package name for Firebase services
// This approach modifies the Google Services plugin behavior directly
project.ext.packageName = 'com.bottlekingng.bottlekingng'

// Override the Google Services task to use the base package name
gradle.projectsEvaluated {
    android.applicationVariants.all { variant ->
        def task = tasks.findByName("process${variant.name.capitalize()}GoogleServices")
        if (task) {
            task.doFirst {
                // Force the package name to be the base one for Firebase matching
                def originalPackageName = 'com.bottlekingng.bottlekingng'
                println "Overriding Google Services package name from ${variant.applicationId} to ${originalPackageName}"
                
                // Create a temporary google-services.json with the correct package name
                def googleServicesFile = file('google-services.json')
                if (googleServicesFile.exists()) {
                    def json = new groovy.json.JsonSlurper().parseText(googleServicesFile.text)
                    
                    // Check if we need to add the variant's package name to the Firebase config
                    def hasVariantPackage = json.client.any { client ->
                        client.client_info.android_client_info.package_name == variant.applicationId
                    }
                    
                    if (!hasVariantPackage) {
                        // Clone the base package configuration for this variant
                        def baseClient = json.client.find { client ->
                            client.client_info.android_client_info.package_name == originalPackageName
                        }
                        
                        if (baseClient) {
                            def newClient = new groovy.json.JsonBuilder(baseClient).toPrettyString()
                            def newClientObj = new groovy.json.JsonSlurper().parseText(newClient)
                            newClientObj.client_info.android_client_info.package_name = variant.applicationId
                            newClientObj.client_info.mobilesdk_app_id = baseClient.client_info.mobilesdk_app_id.replace(originalPackageName, variant.applicationId)
                            
                            json.client.add(newClientObj)
                            
                            // Write the modified JSON back
                            def modifiedJson = new groovy.json.JsonBuilder(json).toPrettyString()
                            googleServicesFile.text = modifiedJson
                            
                            println "Added Firebase configuration for package: ${variant.applicationId}"
                        }
                    }
                }
            }
        }
    }
}
