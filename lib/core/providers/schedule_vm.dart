import 'package:bottle_king_mobile/core/core.dart';

class ScheduleVm extends BaseVm {
  DeliveryType _deliveryType = DeliveryType.now;
  DeliveryType get deliveryType => _deliveryType;
  bool get isSchedule => _deliveryType == DeliveryType.schedule;
  setScheduleDeliveryType(DeliveryType deliveryType) {
    if (deliveryType == DeliveryType.now) {
      removeScheduling();
    }
    _deliveryType = deliveryType;
  }

  DateTime? _scheculeDate;
  DateTime? get scheculeDate => _scheculeDate;
  String get formatedScheduleDate =>
      AppUtils.dayWithSuffixMonthAndYear(_scheculeDate ?? DateTime.now());
  setScheduleDate(DateTime date) {
    _scheculeDate = date;

    reBuildUI();
  }

  String? _scheduleTimeString;
  String? get scheduleTimeString => _scheduleTimeString;
  String? formatedScheduleTime;
  setScheduleTimeString({required String time, required formattedTime}) {
    _scheduleTimeString = time;
    formatedScheduleTime = formattedTime;

    reBuildUI();
  }

  void removeScheduling() {
    _scheculeDate = null;
    _scheduleTimeString = null;
  }
}

final scheduleVmodel = ChangeNotifierProvider((ref) {
  return ScheduleVm();
});
