import 'package:bottle_king_mobile/core/core.dart';

const String getCartState = "getCartState";
const String getWishlistState = "getWishlistState";
const String removeState = "removeState";
const String reorderState = "reorderState";
const String rewardGenerateCouponState = "rewardGenerateCouponState";

class CartVm extends BaseVm {
  CartModel? _cartModel;
  CartModel? get cartModel => _cartModel;
  CartProducts? get specialCartProduct => _cartModel?.items?.specialProducts;
  CartProducts? get regularCartProduct => _cartModel?.items?.regularProducts;
  CartModel? _wishList;
  List<CartItem> get wishList => _wishList?.items?.regularProducts?.items ?? [];
  int get totalCartQty => _cartModel?.items?.regularProducts?.count ?? 0;

  // Synchronization state for cart updates
  int _pendingUpdateCount = 0;
  final Set<String> _updatingItemIds = {};
  final Map<String, Future<void>> _itemUpdateQueues = {};
  bool get hasPendingUpdates => _pendingUpdateCount > 0;
  Set<String> get updatingItemIds => _updatingItemIds;

  Future<ApiResponse> addToCartOrWishlist({
    required String productId,
    required String variationId,
    String quantity = "1",
    bool isWishList = false,
  }) async {
    printty("addToCartOrWishlist productId: $productId");
    return await performApiCall(
      url: "/cart/add",
      method: apiService.postWithAuth,
      isFormData: false,
      body: {
        "productId": productId,
        "variationId": variationId,
        "quantity": quantity,
        "isWishList": isWishList
      },
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        AnalyticsService.instance.trackAddToCart(
          productId: productId,
          variationId: variationId,
          quantity: int.tryParse(quantity) ?? 1,
        );
        return ApiResponse(
          success: true,
          message: isWishList
              ? "Wishlist updated! Good taste! "
              : "Nice choice! It’s chilling in your cart.",
        );
      },
    );
  }

  Future<ApiResponse> updateCartOrWishlist({
    required String itemId,
    int quantity = 1,
    bool isWishList = false,
  }) async {
    printty("updateCartOrWishlist itemId: $itemId, qty: $quantity");

    // Increase pending count and mark item as updating
    _pendingUpdateCount++;
    _updatingItemIds.add(itemId);
    notifyListeners();

    Future<ApiResponse> performUpdate() async {
      try {
        final res = await performApiCall(
          url: "/cart/update",
          method: apiService.putWithAuth,
          isFormData: true,
          body: {
            "itemId": itemId,
            "quantity": quantity,
            "isWishList": isWishList,
          },
          onSuccess: (data) {
            _cartModel = cartModelFromJson(json.encode(data["data"]));
            return apiResponse;
          },
        );
        return res;
      } finally {
        _pendingUpdateCount = (_pendingUpdateCount - 1).clamp(0, 1 << 30);
        _updatingItemIds.remove(itemId);
        notifyListeners();
      }
    }

    // Ensure per-item sequential updates to maintain consistency
    if (_itemUpdateQueues.containsKey(itemId)) {
      // Wait for previous update for this item to finish
      await _itemUpdateQueues[itemId];
    }
    final future = performUpdate();
    // Track queue completion (do not throw further)
    _itemUpdateQueues[itemId] = future.then((_) {}, onError: (_) {});
    final result = await future;
    // Clean up queue if completed
    if (_itemUpdateQueues[itemId] == future) {
      _itemUpdateQueues.remove(itemId);
    }
    return result;
  }

  Future<ApiResponse> getCart({
    bool showLoader = true,
  }) async {
    return await performApiCall(
      url: "/cart?wishlist=false",
      method: apiService.getWithAuth,
      busyObjectName: showLoader ? getCartState : "",
      onSuccess: (data) {
        _cartModel = cartModelFromJson(json.encode(data["data"]));
        return ApiResponse(success: true, data: _cartModel);
      },
    );
  }

  Future<ApiResponse> getWishlist({
    bool showLoader = true,
  }) async {
    return await performApiCall(
      url: "/cart?wishlist=true",
      method: apiService.getWithAuth,
      busyObjectName: showLoader ? getWishlistState : "",
      onSuccess: (data) {
        _wishList = cartModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> removeCartOrWishlist({
    required String itemId,
    bool isWishList = false,
  }) async {
    printty("removeCartOrWishlist productId: $itemId");
    return await performApiCall(
      url: "/cart/remove/$itemId?isWishList=$isWishList",
      method: apiService.deleteWithAuth,
      busyObjectName: removeState,
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> clearCartOrWishlist({
    bool isWishList = false,
  }) async {
    return await performApiCall(
      url: "/cart/clear?isWishList=$isWishList",
      method: apiService.deleteWithAuth,
      busyObjectName: removeState,
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> reorder({
    required String orderId,
  }) async {
    return await performApiCall(
      url: "/order/reorder/$orderId",
      method: apiService.postWithAuth,
      busyObjectName: reorderState,
      onSuccess: (data) {
        getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  /// Optimistically removes an item from the cart and attempts to sync with backend
  /// If the backend call fails, the item is restored and an error is shown
  Future<ApiResponse> removeCartItemOptimistically({
    required String itemId,
    bool isWishList = false,
  }) async {
    // Store the original cart state for potential rollback
    final originalCartModel = _cartModel;
    final originalWishList = _wishList;

    // Find the item to remove
    final targetList = isWishList ? _wishList : _cartModel;
    final itemToRemove = targetList?.items?.regularProducts?.items
        ?.firstWhere((item) => item.id == itemId, orElse: () => CartItem());

    if (itemToRemove?.id == null) {
      return ApiResponse(
        success: false,
        message: "Item not found in cart",
      );
    }

    // Optimistically update the UI by removing the item
    _removeItemFromLocalState(itemId, isWishList);

    // Notify listeners to update UI immediately
    notifyListeners();

    try {
      // Attempt to remove from backend
      final response = await performApiCall(
        url: "/cart/remove/$itemId?isWishList=$isWishList",
        method: apiService.deleteWithAuth,
        busyObjectName:
            "", // Don't show loading since we're doing optimistic update
        onSuccess: (data) {
          // Backend success - refresh cart to ensure consistency
          isWishList
              ? getWishlist(showLoader: false)
              : getCart(showLoader: false);
          return apiResponse;
        },
      );

      if (!response.success) {
        // Backend failed - restore original state
        _restoreOriginalState(originalCartModel, originalWishList, isWishList);
        notifyListeners();

        // Show error message
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message:
              response.message ?? "Failed to remove item. Please try again.",
        );
      }

      return response;
    } catch (e) {
      // Exception occurred - restore original state
      _restoreOriginalState(originalCartModel, originalWishList, isWishList);
      notifyListeners();

      // Show error message
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: "Failed to remove item. Please check your connection.",
      );

      return ApiResponse(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Removes an item from local state (optimistic update)
  void _removeItemFromLocalState(String itemId, bool isWishList) {
    if (isWishList && _wishList != null) {
      final updatedItems = _wishList!.items?.regularProducts?.items
              ?.where((item) => item.id != itemId)
              .toList() ??
          [];
      final updatedTotal = _calculateTotal(updatedItems);

      _wishList = _wishList!.copyWith(
        items: _wishList!.items?.copyWith(
          regularProducts: _wishList!.items?.regularProducts?.copyWith(
            items: updatedItems,
            total: updatedTotal,
          ),
        ),
      );
    } else if (!isWishList && _cartModel != null) {
      final updatedItems = _cartModel!.items?.regularProducts?.items
              ?.where((item) => item.id != itemId)
              .toList() ??
          [];
      final updatedTotal = _calculateTotal(updatedItems);

      _cartModel = _cartModel!.copyWith(
        items: _cartModel!.items?.copyWith(
          regularProducts: _cartModel!.items?.regularProducts?.copyWith(
            items: updatedItems,
            total: updatedTotal,
          ),
        ),
      );
    }
  }

  /// Restores the original cart state in case of backend failure
  void _restoreOriginalState(
      CartModel? originalCart, CartModel? originalWishList, bool isWishList) {
    if (isWishList) {
      _wishList = originalWishList;
    } else {
      _cartModel = originalCart;
    }
  }

  /// Calculates the total price for a list of cart items
  double _calculateTotal(List<CartItem> items) {
    return items.fold<double>(0, (total, item) {
      final itemPrice = item.price ?? 0;
      final itemQuantity = item.quantity ?? 1;
      return total + (itemPrice * itemQuantity);
    });
  }

  /// Waits until all pending cart updates are finished
  Future<void> waitForAllUpdates() async {
    if (!hasPendingUpdates) return;
    await Future.wait(_itemUpdateQueues.values.toList());
  }
}

final cartVm = ChangeNotifierProvider((ref) {
  return CartVm();
});
