import 'package:bottle_king_mobile/core/core.dart';

const String validateCouponState = "validateCouponState";
const String verifyReferralCodeState = "verifyReferralCodeState";

class CouponVm extends BaseVm {
  Future<ApiResponse<Coupon>> rewardGenerateCoupon(
    String customerId,
  ) async {
    return await performApiCall<Coupon>(
      url: "/reward/generate-coupon",
      method: apiService.postWithAuth,
      busyObjectName: rewardGenerateCouponState,
      body: {"customerId": customerId},
      onSuccess: (data) {
        // getCart(showLoader: false);
        return ApiResponse<Coupon>(
          success: true,
          data: Coupon.fromJson(data["data"]["coupon"]),
        );
      },
    );
  }

  Future<ApiResponse> validateCoupon(
    String code,
  ) async {
    return await performApiCall(
      url: "/coupon/validate?code=$code",
      method: apiService.getWithAuth,
      busyObjectName: validateCouponState,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: data["data"]?["value"],
        );
      },
    );
  }

  Future<ApiResponse> verifyReferralCode(
    String code,
  ) async {
    return await performApiCall(
      url: "/checkout/verify-referral?referralCode=$code",
      method: apiService.getWithAuth,
      busyObjectName: validateCouponState,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: data["data"]?["value"],
        );
      },
    );
  }
}

final couponVmodel = ChangeNotifierProvider<CouponVm>((ref) => CouponVm());
