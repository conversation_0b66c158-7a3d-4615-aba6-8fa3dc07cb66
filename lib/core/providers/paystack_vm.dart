// import 'package:bottle_king_mobile/core/core.dart';
// import 'package:flutter/services.dart';
// import 'package:paystack_flutter_sdk/paystack_flutter_sdk.dart';

// class PaystackVm extends BaseVm {
//   final _accessCode = "";
//   final _paystack = Paystack();
//   String _reference = "";

//   launch() async {
//     String reference = "";
//     try {
//       final response = await _paystack.launch(_accessCode);
//       if (response.status == "success") {
//         reference = response.reference;
//         printty(reference);
//       } else if (response.status == "cancelled") {
//         printty(response.message);
//       } else {
//         printty(response.message);
//       }
//     } on PlatformException catch (e) {
//       printty(e.message!);
//     }
//     _reference = reference;
//     reBuildUI();
//   }

//   Future<ApiResponse> initializeSDKEndpoint({
//     required String email,
//     required String amount,
//   }) async {
//     return await performApiCall(
//       url: "https://api.paystack.co/transaction/initialize",
//       method: apiService.paystack,
//       body: {
//         "email": email,
//         "amount": amount,
//       },
//       onSuccess: (data) {
//         printty("initializeSDKEndpoint: $data");
//         return apiResponse;
//       },
//     );
//   }
// }
