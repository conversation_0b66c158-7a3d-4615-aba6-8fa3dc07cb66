import 'package:bottle_king_mobile/core/core.dart';

class GroupOrderVm extends BaseVm {
  Future<ApiResponse> getGroupOrders({
    required String code,
  }) async {
    return await performApiCall(
      url: "/group-order/?uniqueCode=$code",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> createGroupOrder(GroupOrderParams params) async {
    final body = params.toJson();
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/group-order/create",
      method: apiService.postWithAuth,
      isFormData: true,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}

final groupOrderVmodel = ChangeNotifierProvider((ref) => GroupOrderVm());
