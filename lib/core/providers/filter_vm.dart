import 'package:bottle_king_mobile/core/core.dart';

class FilterVm extends BaseVm {
  FilterModel _currentFilter = const FilterModel();
  FilterModel get currentFilter => _currentFilter;

  // Temporary filter state for the modal
  FilterModel _tempFilter = const FilterModel();
  FilterModel get tempFilter => _tempFilter;

  // Initialize temp filter with current filter when opening modal
  void initializeTempFilter() {
    _tempFilter = _currentFilter;
    reBuildUI();
  }

  // Update temp filter during modal interaction
  void updateTempFilter(FilterModel filter) {
    _tempFilter = filter;
    reBuildUI();
  }

  // Apply temp filter to current filter
  void applyFilter() {
    _currentFilter = _tempFilter.copyWith(
      hasActiveFilters: !_tempFilter.isEmpty,
    );
    reBuildUI();
  }

  // Clear all filters
  void clearAllFilters() {
    _currentFilter = const FilterModel();
    _tempFilter = const FilterModel();
    reBuildUI();
  }

  // Update specific filter properties
  void updateCategory(String? category) {
    _tempFilter = _tempFilter.copyWith(selectedCategory: category);
    reBuildUI();
  }

  void updateSort(String? sort) {
    _tempFilter = _tempFilter.copyWith(selectedSort: sort);
    reBuildUI();
  }

  void updateSize(String? size) {
    _tempFilter = _tempFilter.copyWith(selectedSize: size);
    reBuildUI();
  }

  void updatePriceRange(double? minPrice, double? maxPrice) {
    _tempFilter = _tempFilter.copyWith(
      minPrice: minPrice,
      maxPrice: maxPrice,
    );
    reBuildUI();
  }

  // Get filter display text for UI
  String getFilterDisplayText() {
    if (_currentFilter.isEmpty) return 'Filter';

    List<String> activeFilters = [];

    if (_currentFilter.selectedCategory != null) {
      activeFilters.add('Category');
    }

    if (_currentFilter.selectedSort != null) {
      activeFilters.add('Sort');
    }

    if (_currentFilter.selectedSize != null) {
      activeFilters.add('cartonSize');
    }

    if (_currentFilter.minPrice != null || _currentFilter.maxPrice != null) {
      activeFilters.add('Price');
    }

    if (activeFilters.isEmpty) return 'Filter';
    if (activeFilters.length == 1) return activeFilters.first;
    return '${activeFilters.length} filters';
  }

  // Check if specific filter type is active
  bool isCategoryFilterActive() => _currentFilter.selectedCategory != null;
  bool isSortFilterActive() => _currentFilter.selectedSort != null;
  bool isPriceFilterActive() =>
      _currentFilter.minPrice != null || _currentFilter.maxPrice != null;
  bool isSizeFilterActive() => _currentFilter.selectedSize != null;

  // Get current filter values for API calls
  Map<String, dynamic> getFilterParams() {
    Map<String, dynamic> params = {};

    if (_currentFilter.selectedCategory != null) {
      params['category'] = _currentFilter.selectedCategory;
    }

    if (_currentFilter.selectedSort != null) {
      final sortOption = SortOption.options.firstWhere(
        (option) => option.value == _currentFilter.selectedSort,
        orElse: () => SortOption.options.first,
      );

      if (sortOption.isExtraCategory) {
        params['extra_category'] = sortOption.value;
      } else {
        params['sortBy'] = sortOption.value;
      }
    }

    if (_currentFilter.selectedSize != null) {
      params['size'] = _currentFilter.selectedSize;
    }

    if (_currentFilter.minPrice != null && _currentFilter.minPrice! > 0) {
      params['min_price'] = _currentFilter.minPrice;
    }

    if (_currentFilter.maxPrice != null && _currentFilter.maxPrice! > 0) {
      params['max_price'] = _currentFilter.maxPrice;
    }

    return params;
  }
}

final filterVm = ChangeNotifierProvider((ref) => FilterVm());
