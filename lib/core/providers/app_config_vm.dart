import 'dart:io';

import 'package:bottle_king_mobile/lib.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppConfigVm extends BaseVm {
  String? _myAppCurrentVersion;
  String? get myAppCurrentVersion => _myAppCurrentVersion;

  AppConfigModel? _configData;
  AppConfigModel? get configData => _configData;
  bool get showPromo =>
      _configData?.tierPromo == true || EnvironmentConfig.isDevelopment;

  bool get appIsDueForUpdate {
    return AppUtils.compareVersions(
      Platform.isAndroid
          ? _configData?.minAndroidVersion ?? "0.0.0"
          : _configData?.minIosVersion ?? "0.0.0",
      _myAppCurrentVersion ?? "0.0.0",
    );
  }

  Future<String> packageInfoInit() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      // await StorageService.storeStringItem(
      //     StorageKey.appVersion, packageInfo.version);

      printty("===> package info initialized... ${packageInfo.version}");
      _myAppCurrentVersion = packageInfo.version;
      return packageInfo.version;
    } catch (e) {
      printty(e.toString(), logName: 'PackageInfo Error check');
      return "";
    }
  }

  /// Fetches app configuration from Firebase
  Future<void> fetchAppConfig() async {
    try {
      setBusyForObject("fetchAppConfig", true);

      final appConfig = await FirebaseAppConfigService.getAppConfig();

      if (appConfig != null) {
        _configData = appConfig;
        printty("App config fetched successfully");
      } else {
        printty("Failed to fetch app config from Firebase");
      }

      notifyListeners();
    } catch (e) {
      printty("Error fetching app config: $e", logName: 'AppConfig Error');
      setErrorForObject("fetchAppConfig", true);
    } finally {
      setBusyForObject("fetchAppConfig", false);
    }
  }

  /// Initializes both package info and app config
  Future<void> initialize() async {
    await packageInfoInit();
    await fetchAppConfig();
  }

  /// Checks if the current app version meets minimum requirements
  bool get isAppVersionSupported {
    if (_configData == null || _myAppCurrentVersion == null) {
      return true; // Default to supported if data is not available
    }

    final minVersion = Platform.isAndroid
        ? _configData!.minAndroidVersion
        : _configData!.minIosVersion;

    if (minVersion == null) {
      return true; // No minimum version requirement
    }

    // Return true if current version is greater than or equal to minimum
    return !AppUtils.compareVersions(minVersion, _myAppCurrentVersion!);
  }
}

final appConfigVmodel = ChangeNotifierProvider((ref) => AppConfigVm());
