import 'package:bottle_king_mobile/core/core.dart';

const String getProductsState = "getProductsState";
const String productSearchState = "productSearchState";
const String productsByCategoryFilter = "productsByCategoryFilter";
const String categorySearchState = "categorySearchState";

class ProductVm extends BaseVm {
  List<ProductCategoryModel> _productCategories = [];
  List<ProductCategoryModel> get productCategories => _productCategories;

  Future<ApiResponse> getProductCategories() async {
    return await performApiCall(
      url: "/product/categories-with-images",
      method: apiService.get,
      onSuccess: (data) {
        _productCategories = productCategoryFromJson(
          json.encode(data["data"]),
        );
        return apiResponse;
      },
    );
  }

  int page = 1;
  int limit = 30;
  int totalNumber = 0;
  List<ProductModel> _productsByCaterory = [];
  List<ProductModel> get productsByCaterory => _productsByCaterory;
  Future<ApiResponse<List<ProductModel>>> getProductsByCategoryFilter({
    String? category,
    bool? isBestSeller,
    bool? isRecommended,
    bool? isNewArrival,
    bool? hasDiscount,
    bool isFirstCall = true,
    String? sortBy, //discount, price_low_high, price_high_low, name
    String? cartonSize,
    String? price,
  }) async {
    if (isFirstCall) {
      printty("isFirstCall");
      page = 1;
    } else {
      page++;
    }
    String bestSeller = isBestSeller == true ? "true" : "";
    String recommended = isRecommended == true ? "true" : "";
    String newArrival = isNewArrival == true ? "true" : "";
    String newDiscount = hasDiscount == true ? "true" : "";

    UriBuilder uriBuilder = UriBuilder("/product")
      // ..addQueryParameterIfNotEmpty("isMobile", "true")
      ..addQueryParameterIfNotEmpty("page", page.toString())
      ..addQueryParameterIfNotEmpty("limit", limit.toString())
      ..addQueryParameterIfNotEmpty("category", category ?? "")
      ..addQueryParameterIfNotEmpty("isBestSeller", bestSeller.toString())
      ..addQueryParameterIfNotEmpty("isRecommended", recommended.toString())
      ..addQueryParameterIfNotEmpty("isNewArrival", newArrival.toString())
      ..addQueryParameterIfNotEmpty("hasDiscount", newDiscount.toString())
      ..addQueryParameterIfNotEmpty("sortBy", sortBy ?? "")
      ..addQueryParameterIfNotEmpty("cartonSize", cartonSize ?? "");

    // Only add price parameter if it has a valid value
    if (price != null && price.isNotEmpty) {
      uriBuilder.addQueryParameterIfNotEmpty("price", price);
    }

    printty(uriBuilder.build().toString(),
        logName: "getProductsByCategoryFilters");

    // Only clear if it's the first page
    if (page == 1) {
      _productsByCaterory.clear();
    }

    return await performApiCall<List<ProductModel>>(
      url: uriBuilder.build().toString(),
      busyObjectName: isFirstCall ? productsByCategoryFilter : paginateState,
      method: apiService.get,
      onSuccess: (data) {
        final products = productModelFromJson(json.encode(data["data"]));
        totalNumber = products.length;
        // If it's the first page, replace the list, otherwise append
        if (page == 1) {
          _productsByCaterory = products;
        } else {
          _productsByCaterory.addAll(products);
        }

        return ApiResponse(success: true, data: products);
      },
    );
  }

  List<ProductModel> _products = [];
  List<ProductModel> get products => _products;
  Future<ApiResponse> getProducts({Map<String, dynamic>? args}) async {
    // Convert boolean flags to string values
    final booleanFlags = {
      'isBestSeller': args?['isBestSeller'],
      'isRecommended': args?['isRecommended'],
      'isNewArrival': args?['isNewArrival'],
      'hasDiscount': args?['hasDiscount']
    };

    // Initialize URI builder with base path and limit
    final uriBuilder = UriBuilder("/product")
      ..addQueryParameterIfNotEmpty("limit", "10");

    // Add query parameters from args
    args?.forEach((key, value) {
      // Handle boolean flags specially
      if (booleanFlags.containsKey(key)) {
        final stringValue = booleanFlags[key] == true ? "true" : "";
        uriBuilder.addQueryParameterIfNotEmpty(key, stringValue);
      } else {
        // Handle all other parameters normally
        uriBuilder.addQueryParameterIfNotEmpty(key, value?.toString() ?? '');
      }
    });
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      busyObjectName: getProductsState,
      onSuccess: (data) {
        final products = productModelFromJson(json.encode(data["data"]));
        _products = products;
        return ApiResponse(success: true, data: products);
      },
      onError: (errorMessage) {
        printty("onErrorgotcalled error: $errorMessage");
        _products.clear();
        return ApiResponse(
          success: false,
          message: errorMessage,
        );
      },
    );
  }

  // Search pagination variables
  int searchPage = 1;
  int searchLimit = 20;
  int searchTotalNumber = 0;
  bool hasMoreSearchResults = true;

  List<ProductModel> _searchProducts = [];
  List<ProductModel> get searchProducts => _searchProducts;
  void clearSearchProducts() {
    _searchProducts.clear();
    searchPage = 1;
    hasMoreSearchResults = true;
    reBuildUI();
  }

  Future<ApiResponse<List<ProductModel>>> productsBySearch({
    required String query,
    bool isFirstCall = true,
  }) async {
    if (query.isEmpty) {
      clearSearchProducts();
      setErrorForObject(productSearchState, false); // Clear any previous errors
      return ApiResponse(success: true);
    }

    // Set pagination
    if (isFirstCall) {
      searchPage = 1;
      hasMoreSearchResults = true;
    } else {
      searchPage++;
    }

    // Clear previous error state before making new request
    setErrorForObject(productSearchState, false);

    UriBuilder uriBuilder = UriBuilder("/product")
      ..addQueryParameterIfNotEmpty("search", query)
      ..addQueryParameterIfNotEmpty("page", searchPage.toString())
      ..addQueryParameterIfNotEmpty("limit", searchLimit.toString());

    printty(uriBuilder.build().toString(), logName: "productsBySearch");

    // Only clear if it's the first page
    if (searchPage == 1) {
      _searchProducts.clear();
    }

    return await performApiCall<List<ProductModel>>(
      url: uriBuilder.build().toString(),
      busyObjectName: isFirstCall ? productSearchState : paginateState,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        final products = productModelFromJson(json.encode(data["data"]));
        searchTotalNumber = products.length;

        // Check if we have more results
        hasMoreSearchResults = products.length >= searchLimit;

        // If it's the first page, replace the list, otherwise append
        if (searchPage == 1) {
          _searchProducts = products;
        } else {
          _searchProducts.addAll(products);
        }

        setErrorForObject(productSearchState,
            false); // Ensure error state is cleared on success
        return ApiResponse<List<ProductModel>>(
          success: true,
          data: products,
          message: "Products fetched successfully",
        );
      },
      onError: (errorMessage) {
        printty("Search error: $errorMessage", logName: "productsBySearch");
        setErrorForObject(productSearchState,
            true); // Set error state for this specific operation
        // Don't clear search products on error to maintain previous results
        return ApiResponse(
          success: false,
          message: errorMessage,
        );
      },
    );
  }

  List<ProductModel> _categorySearchResults = [];
  List<ProductModel> get categorySearchResults => _categorySearchResults;

  void clearCategorySearchResults() {
    _categorySearchResults.clear();
    reBuildUI();
  }

  Future<ApiResponse> searchProductsByCategory({
    required String query,
    String? category,
  }) async {
    if (query.isEmpty) {
      clearCategorySearchResults();
      return ApiResponse(success: true);
    }

    UriBuilder uriBuilder = UriBuilder("/product");
    uriBuilder.addQueryParameterIfNotEmpty("search", query);
    if (category != null && category.isNotEmpty) {
      uriBuilder.addQueryParameterIfNotEmpty("category", category);
    }

    return await performApiCall(
      url: uriBuilder.build().toString(),
      busyObjectName: categorySearchState,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        final products = productModelFromJson(json.encode(data["data"]));
        _categorySearchResults = products;
        return ApiResponse(success: true, data: products);
      },
    );
  }

  Future<ApiResponse> productNotifyMe({
    required String sku,
    required String authEmail,
    required String productId,
    required String productName,
    String? busyStateKey,
  }) async {
    return await performApiCall(
      url: "/store/notifyMe",
      busyObjectName: busyStateKey,
      method: apiService.postWithAuth,
      body: {
        "sku": sku,
        "email": authEmail,
        "productId": productId,
        "productName": productName,
      },
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          message: "You’ll be first to know!",
        );
      },
    );
  }
}

final productVm = ChangeNotifierProvider((ref) {
  return ProductVm();
});
