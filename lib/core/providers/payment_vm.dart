import 'package:bottle_king_mobile/core/core.dart';

class PaymentVm extends BaseVm {
  // PAYSTACK TRANSACTION
  Future<ApiResponse> initiateTransaction({
    required String email,
    required num amount,
    required String deliveryType,
    required String cartId,
    String? referralCode,
  }) async {
    final body = {
      "email": email,
      "amount": amount,
      "deliveryType": deliveryType,
      "cartId": cartId,
      "referralCode": referralCode,
      "platform": "mobile",
    }..removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/checkout/initiate-transaction",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        printty("initiateCheckout data: $data");
        return ApiResponse(success: true, data: data["data"]);
      },
    );
  }

  // MECASH TRANSACTION (PAY WITH BOTTLE KING)
  Future<ApiResponse<MecashModel>> payWithMecash({
    required num amount,
    required String orderId,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/checkout/get-account-number")
      ..addQueryParameterIfNotEmpty("amount", amount.toString())
      ..addQueryParameterIfNotEmpty("order_id", orderId);
    return await performApiCall<MecashModel>(
      url: uriBuilder.build().toString(),
      method: apiService.postWithAuth,
      body: {"amount": amount, "order_id": orderId},
      onSuccess: (data) {
        final mecashModel = MecashModel.fromJson(data["data"]);
        return ApiResponse<MecashModel>(success: true, data: mecashModel);
      },
    );
  }
}

final paymentViewModel = ChangeNotifierProvider((ref) => PaymentVm());
