class GroupOrderParams {
  final String name;
  final DateTime? deadline;
  final DateTime? deliveryDate;
  final String? paymentMethod;
  final List<Participant>? participants;

  GroupOrderParams({
    required this.name,
    this.deadline,
    this.deliveryDate,
    this.paymentMethod,
    this.participants,
  });

  Map<String, dynamic> toJson() => {
        "name": name,
        "deadline": deadline?.toIso8601String(),
        "deliveryDate": deliveryDate?.toIso8601String(),
        "paymentMethod": paymentMethod,
        "participants": participants != null
            ? List<dynamic>.from(participants!.map((x) => x.toJson()))
            : null,
      };
}

class Participant {
  final String? email;
  final String? fullname;

  Participant({
    this.email,
    this.fullname,
  });

  Map<String, dynamic> toJson() => {
        "email": email,
        "fullname": fullname,
      };
}
