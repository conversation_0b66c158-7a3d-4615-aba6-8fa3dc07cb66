import 'dart:convert';

CartTierModel cartTierModelFromJson(String str) =>
    CartTierModel.fromJson(json.decode(str));

String cartTierModelToJson(CartTierModel data) => json.encode(data.toJson());

class CartTierModel {
  final num? currentTotal;
  final NextTier? nextTier;
  final num? amountToNextTier;

  CartTierModel({
    this.currentTotal,
    this.nextTier,
    this.amountToNextTier,
  });

  factory CartTierModel.fromJson(Map<String, dynamic> json) => CartTierModel(
        currentTotal: json["currentTotal"],
        nextTier: json["nextTier"] == null
            ? null
            : NextTier.fromJson(json["nextTier"]),
        amountToNextTier: json["amountToNextTier"],
      );

  Map<String, dynamic> toJson() => {
        "currentTotal": currentTotal,
        "nextTier": nextTier?.toJson(),
        "amountToNextTier": amountToNextTier,
      };
}

class NextTier {
  final num? amount;
  final String? prize;
  final int? tickets;

  NextTier({
    this.amount,
    this.prize,
    this.tickets,
  });

  factory NextTier.fromJson(Map<String, dynamic> json) => NextTier(
        amount: json["amount"],
        prize: json["prize"],
        tickets: json["tickets"],
      );

  Map<String, dynamic> toJson() => {
        "amount": amount,
        "prize": prize,
        "tickets": tickets,
      };
}
