import 'dart:convert';

List<SliderModel> sliderModelFromJson(String str) => List<SliderModel>.from(
    json.decode(str).map((x) => SliderModel.fromJson(x)));

String sliderModelToJson(List<SliderModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SliderModel {
  final String? id;
  final String? photoUrl;
  final String? section;
  final String? target;
  final int? displayOrder;
  final bool? isActive;
  final String? sku;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final int? v;

  SliderModel({
    this.id,
    this.photoUrl,
    this.section,
    this.target,
    this.displayOrder,
    this.isActive,
    this.sku,
    this.createdAt,
    this.lastUpdated,
    this.v,
  });

  factory SliderModel.fromJson(Map<String, dynamic> json) => SliderModel(
        id: json["_id"],
        photoUrl: json["photo_url"],
        section: json["section"],
        target: json["target"],
        displayOrder: json["displayOrder"],
        isActive: json["isActive"],
        sku: json["sku"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "photo_url": photoUrl,
        "section": section,
        "target": target,
        "displayOrder": displayOrder,
        "isActive": isActive,
        "sku": sku,
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "__v": v,
      };
}
