import 'dart:convert';

RateStatusModel rateStatusModelFromJson(String str) =>
    RateStatusModel.fromJson(json.decode(str));

String rateStatusModelToJson(RateStatusModel data) =>
    json.encode(data.toJson());

class RateStatusModel {
  final String? orderId;
  final Ces? ces;
  final Ces? csat;

  RateStatusModel({
    this.orderId,
    this.ces,
    this.csat,
  });

  factory RateStatusModel.fromJson(Map<String, dynamic> json) =>
      RateStatusModel(
        orderId: json["orderId"],
        ces: json["ces"] == null ? null : Ces.fromJson(json["ces"]),
        csat: json["csat"] == null ? null : Ces.fromJson(json["csat"]),
      );

  Map<String, dynamic> toJson() => {
        "orderId": orderId,
        "ces": ces?.toJson(),
        "csat": csat?.toJson(),
      };
}

class Ces {
  final bool? submitted;
  final int? rating;
  final String? ratingDescription;
  final DateTime? submittedAt;

  Ces({
    this.submitted,
    this.rating,
    this.ratingDescription,
    this.submittedAt,
  });

  factory Ces.fromJson(Map<String, dynamic> json) => Ces(
        submitted: json["submitted"],
        rating: json["rating"],
        ratingDescription: json["ratingDescription"],
        submittedAt: json["submittedAt"] == null
            ? null
            : DateTime.parse(json["submittedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "submitted": submitted,
        "rating": rating,
        "ratingDescription": ratingDescription,
        "submittedAt": submittedAt?.toIso8601String(),
      };
}
