import 'dart:convert';

MecashModel mecashModelFromJson(String str) =>
    MecashModel.fromJson(json.decode(str));

String mecashModelToJson(MecashModel data) => json.encode(data.toJson());

class MecashModel {
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? reference;
  final Account? account;
  final String? status;
  final String? currency;
  final String? country;
  final bool? isPermanent;
  final int? expiryTime;
  final DateTime? created;

  MecashModel({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.reference,
    this.account,
    this.status,
    this.currency,
    this.country,
    this.isPermanent,
    this.expiryTime,
    this.created,
  });

  factory MecashModel.fromJson(Map<String, dynamic> json) => MecashModel(
        id: json["id"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        reference: json["reference"],
        account:
            json["account"] == null ? null : Account.from<PERSON>son(json["account"]),
        status: json["status"],
        currency: json["currency"],
        country: json["country"],
        isPermanent: json["isPermanent"],
        expiryTime: json["expiryTime"],
        created:
            json["created"] == null ? null : DateTime.parse(json["created"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "reference": reference,
        "account": account?.toJson(),
        "status": status,
        "currency": currency,
        "country": country,
        "isPermanent": isPermanent,
        "expiryTime": expiryTime,
        "created": created?.toIso8601String(),
      };
}

class Account {
  final String? bankName;
  final String? number;

  Account({
    this.bankName,
    this.number,
  });

  factory Account.fromJson(Map<String, dynamic> json) => Account(
        bankName: json["bankName"],
        number: json["number"],
      );

  Map<String, dynamic> toJson() => {
        "bankName": bankName,
        "number": number,
      };
}
