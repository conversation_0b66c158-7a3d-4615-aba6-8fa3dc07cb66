import 'dart:convert';

DeliveryFee deliveryFeeFromJson(String str) =>
    DeliveryFee.fromJson(json.decode(str));

String deliveryFeeToJson(DeliveryFee data) => json.encode(data.toJson());

class DeliveryFee {
  final String? distance;
  final String? duration;
  final int? cost;

  DeliveryFee({
    this.distance,
    this.duration,
    this.cost,
  });

  factory DeliveryFee.fromJson(Map<String, dynamic> json) => DeliveryFee(
        distance: json["distance"],
        duration: json["duration"],
        cost: json["cost"],
      );

  Map<String, dynamic> toJson() => {
        "distance": distance,
        "duration": duration,
        "cost": cost,
      };
}
