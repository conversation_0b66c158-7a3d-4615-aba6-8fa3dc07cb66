import 'dart:convert';

RewardTierModel rewardTierModelFromJson(String str) =>
    RewardTierModel.fromJson(json.decode(str));

String rewardTierModelToJson(RewardTierModel data) =>
    json.encode(data.toJson());

class RewardTierModel {
  final String? id;
  final String? name;
  final String? type;
  final int? v;
  final DateTime? createdAt;
  final int? tickets;
  final int? tierAmount;
  final String? value;

  RewardTierModel({
    this.id,
    this.name,
    this.type,
    this.v,
    this.createdAt,
    this.tickets,
    this.tierAmount,
    this.value,
  });

  factory RewardTierModel.fromJson(Map<String, dynamic> json) =>
      RewardTierModel(
        id: json["_id"],
        name: json["name"],
        type: json["type"],
        v: json["__v"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        tickets: json["tickets"],
        tierAmount: json["tierAmount"],
        value: json["value"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "type": type,
        "__v": v,
        "createdAt": createdAt?.toIso8601String(),
        "tickets": tickets,
        "tierAmount": tierAmount,
        "value": value,
      };
}
