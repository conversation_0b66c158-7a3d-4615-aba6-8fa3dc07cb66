import 'dart:convert';

List<ProductCategoryModel> productCategoryFromJson(String str) =>
    List<ProductCategoryModel>.from(
        json.decode(str).map((x) => ProductCategoryModel.fromJson(x)));

String productCategoryToJson(List<ProductCategoryModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ProductCategoryModel {
  final String? category;
  final dynamic image;
  final dynamic sliderId;
  final int? displayOrder;

  ProductCategoryModel({
    this.category,
    this.image,
    this.sliderId,
    this.displayOrder,
  });

  factory ProductCategoryModel.fromJson(Map<String, dynamic> json) =>
      ProductCategoryModel(
        category: json["category"],
        image: json["image"],
        sliderId: json["sliderId"],
        displayOrder: json["displayOrder"],
      );

  Map<String, dynamic> toJson() => {
        "category": category,
        "image": image,
        "sliderId": sliderId,
        "displayOrder": displayOrder,
      };
}
