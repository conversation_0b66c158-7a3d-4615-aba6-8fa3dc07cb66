import 'dart:convert';

List<AddressModel> addressFromJson(String str) => List<AddressModel>.from(
    json.decode(str).map((x) => AddressModel.fromJson(x)));

String addressToJson(List<AddressModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AddressModel {
  final String? title;
  final String? type;
  final String? city;
  final String? state;
  final String? country;
  final String? fullAddress;
  final double? lat;
  final double? lng;
  final bool? isDefault;
  final dynamic storeId;
  final DateTime? lastUsed;
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AddressModel({
    this.title,
    this.type,
    this.city,
    this.state,
    this.country,
    this.fullAddress,
    this.lat,
    this.lng,
    this.isDefault,
    this.storeId,
    this.lastUsed,
    this.id,
    this.createdAt,
    this.updatedAt,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => AddressModel(
        title: json["title"],
        type: json["type"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        fullAddress: json["fullAddress"],
        lat: json["lat"]?.toDouble(),
        lng: json["lng"]?.toDouble(),
        isDefault: json["isDefault"],
        storeId: json["storeId"],
        lastUsed:
            json["lastUsed"] == null ? null : DateTime.parse(json["lastUsed"]),
        id: json["_id"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "type": type,
        "city": city,
        "state": state,
        "country": country,
        "fullAddress": fullAddress,
        "lat": lat,
        "lng": lng,
        "isDefault": isDefault,
        "storeId": storeId,
        "lastUsed": lastUsed?.toIso8601String(),
        "_id": id,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}
