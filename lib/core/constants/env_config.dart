import 'package:bottle_king_mobile/core/core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvConfig {
  static Future<void> initialize(String flavor) async {
    // final flavor = const String.fromEnvironment('FLAVOR', defaultValue: 'prod');
    final envFile = '.env.$flavor';

    try {
      // Load the appropriate .env file based on flavor
      await dotenv.load(fileName: envFile);
      printty('Loaded $envFile');

      // Set environment
      EnvironmentConfig.environment = Environment.values.firstWhere(
        (e) => e.name == flavor,
        orElse: () => throw Exception('Unknown flavor: $flavor'),
      );
    } on Exception catch (e) {
      printty('Error initializing environment: $e');
      rethrow;
    }
  }

  // Environment variables
  static String get baseUrl => dotenv.env['BASE_URL'] ?? '';
  static String get googlePlacesApiKey =>
      dotenv.env['GOOGLE_PLACES_API_KEY'] ?? '';
  static String get googleMapApiKey => dotenv.env['GOOGLE_MAP_API_KEY'] ?? '';
  static String get paystackKey => dotenv.env['PAYSTACK_KEY'] ?? '';
  static String get paystackSecretKey =>
      dotenv.env['PAYSTACK_SCREET_KEY'] ?? '';
  static String get frontendUrl => dotenv.env['FRONTEND_URL'] ?? '';
  static String get mixpanelProjectToken =>
      dotenv.env['MIXPANEL_PROJECT_TOKEN'] ?? '';
  // static String get testPROD => dotenv.env['TEST_PROD'] ?? '';
}

// flutter build apk --flavor dev --target lib/main_dev.dart
// flutter build apk --flavor staging --target lib/main_staging.dart
// flutter build apk --flavor prod --target lib/main_prod.dart

// flutter build appbundle --flavor dev -t lib/main_dev.dart
// flutter build appbundle --flavor staging -t lib/main_staging.dart
// flutter build appbundle --flavor prod -t lib/main_prod.dart

// Shorebird (android)
// shorebird release android --flavor prod -t lib/main_prod.dart

// Shorebird (IOS)
// shorebird release ios --flavor prod -t lib/main_prod.dart

// Shorebird (patch android)
// shorebird patch android --flavor prod -t lib/main_prod.dart

// Shorebird (patch IOS)
// shorebird patch ios --flavor prod -t lib/main_prod.dart



// Certificate fingerprints:
// SHA1: 1C:41:51:9C:20:11:02:E7:5C:97:AB:13:90:5F:40:87:3A:CD:97:E3
// SHA256: 04:C2:12:57:02:D4:43:5F:ED:7A:F4:8F:CC:11:FE:8C:D6:42:65:58:52:D5:B2:B0:EE:C2:C5:DF:43:3F:CE:DF