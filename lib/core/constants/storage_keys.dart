class StorageKey {
  static const String generalHiveBox = "generalHiveBox";
  static const String accessToken = "accessToken";
  static const String refreshToken = "refreshToken";
  static const String authUser = "authUser";
  static const String email = "email";

  static const String deviceToken = "deviceToken";
  static const String deviceId = "deviceId";
  static const String deviceType = "deviceType";
  static const String deviceName = "deviceName";
  static const String deviceModel = "deviceModel";

  static const String notificationToken = "notificationToken";
  static const String appVersion = "appVersion";

  static const String productCart = "productCart";
  static const String csatKey = "csatKey";

  // Deep Link
  static const String referralCode = "referralCode";
  static const String promoCode = "promoCode";

  // static const String analyticsOptOut = "analyticsOptOut";
  // static const String mixpanelDistinctId = "mixpanelDistinctId";
}
