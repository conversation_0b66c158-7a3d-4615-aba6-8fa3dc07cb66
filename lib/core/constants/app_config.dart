// import 'package:bottle_king_mobile/core/core.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';

// /// Centralized configuration management for the application.
// /// Handles environment-specific settings and provides safe access to configuration values.
// class AppConfig {
//   static late final Map<String, dynamic> _config;
//   static late final EnvironmentType _currentEnvironment;

//   /// Initializes the configuration for the specified environment.
//   /// Must be called before accessing any configuration values.
//   static void setEnvironment(EnvironmentType env) {
//     _currentEnvironment = env;
//     switch (env) {
//       case EnvironmentType.dev:
//         _config = _ConfigConstants.devConstants;
//         break;
//       case EnvironmentType.staging:
//         _config = _ConfigConstants.stagingConstants;
//         break;
//       case EnvironmentType.prod:
//         _config = _ConfigConstants.prodConstants;
//         break;
//     }
//     _validateConfig();
//   }

//   /// Validates that required configuration values are present
//   static void _validateConfig() {
//     if (baseUrl.isEmpty) {
//       throw Exception(
//           'Base URL is not configured for $_currentEnvironment environment');
//     }
//     // Add other critical validations as needed
//   }

//   /// Gets the current environment type
//   static EnvironmentType get environment => _currentEnvironment;

//   /// Gets the base API URL
//   static String get baseUrl => _config[_ConfigConstants.baseUrl] ?? '';

//   /// Gets the PayStack public key
//   static String get payStackKey => _config[_ConfigConstants.payStackKey] ?? '';

//   /// Gets the Google Places API key
//   static String get googlePlacesApiKey =>
//       _config[_ConfigConstants.googlePlacesApiKey] ?? '';

//   /// Gets the Google Maps API key
//   static String get googleMapsApiKey =>
//       _config[_ConfigConstants.googleMapsApiKey] ?? '';
// }

// /// Contains all configuration constants and environment-specific values
// class _ConfigConstants {
//   // Configuration keys
//   static const String baseUrl = 'baseUrl';
//   static const String payStackKey = 'payStackPublicKey';
//   static const String googlePlacesApiKey = 'googlePlacesApiKey';
//   static const String googleMapsApiKey = 'googleMapsApiKey';

//   // Development environment constants
//   static final Map<String, dynamic> devConstants = {
//     baseUrl: _getEnv('DEV_URL', 'Development base URL'),
//     payStackKey: _getEnv('PAYSTACK_TEST_KEY'),
//     googlePlacesApiKey: _getEnv('GOOGLE_PLACES_API_KEY'),
//     googleMapsApiKey: _getEnv('GOOGLE_MAPS_API_KEY'),
//   };

//   // Staging environment constants
//   static final Map<String, dynamic> stagingConstants = {
//     baseUrl: _getEnv('STAGING_URL', 'Staging base URL'),
//     payStackKey: _getEnv('PAYSTACK_TEST_KEY'),
//     googlePlacesApiKey: _getEnv('GOOGLE_PLACES_API_KEY'),
//     googleMapsApiKey: _getEnv('GOOGLE_MAPS_API_KEY'),
//   };

//   // Production environment constants
//   static final Map<String, dynamic> prodConstants = {
//     baseUrl: _getEnv('PROD_URL', 'Production base URL'),
//     payStackKey: _getEnv('PAYSTACK_LIVE_KEY'),
//     googlePlacesApiKey: _getEnv('GOOGLE_PLACES_API_KEY'),
//     googleMapsApiKey: _getEnv('GOOGLE_MAPS_API_KEY'),
//   };

//   /// Helper method to get environment variables with optional description for error messages
//   static String _getEnv(String key, [String? description]) {
//     final value = dotenv.env[key] ?? '';
//     if (value.isEmpty) {
//       printty(
//           'Warning: Environment variable $key (${description ?? key}) is not set');
//     }
//     return value;
//   }
// }
