import 'package:bottle_king_mobile/core/core.dart';

/// Helper class for testing authentication and token refresh functionality
/// This class provides methods to simulate various authentication scenarios
class AuthTestHelper {
  /// Tests the token refresh functionality
  static Future<void> testTokenRefresh() async {
    printty("=== Testing Token Refresh Functionality ===", logName: "Auth Test");
    
    try {
      // Check if user has valid tokens
      final hasTokens = await _hasValidTokens();
      printty("Has valid tokens: $hasTokens", logName: "Auth Test");
      
      if (hasTokens) {
        // Test refresh token functionality
        final newToken = await TokenRefreshService.refreshAccessToken();
        if (newToken != null) {
          printty("✅ Token refresh successful", logName: "Auth Test");
          printty("New token: ${newToken.substring(0, 20)}...", logName: "Auth Test");
        } else {
          printty("❌ Token refresh failed", logName: "Auth Test");
        }
      } else {
        printty("⚠️ No tokens available for testing", logName: "Auth Test");
      }
    } catch (e) {
      printty("❌ Token refresh test error: $e", logName: "Auth Test");
    }
  }
  
  /// Tests the authentication state
  static Future<void> testAuthState() async {
    printty("=== Testing Authentication State ===", logName: "Auth Test");
    
    try {
      final accessToken = await StorageService.getAccessToken();
      final refreshToken = await StorageService.getRefreshToken();
      final user = await StorageService.getUser();
      
      printty("Access token exists: ${accessToken != null}", logName: "Auth Test");
      printty("Refresh token exists: ${refreshToken != null}", logName: "Auth Test");
      printty("User data exists: ${user != null}", logName: "Auth Test");
      
      if (user != null) {
        printty("User: ${user.firstname} ${user.lastname}", logName: "Auth Test");
        printty("Email: ${user.email}", logName: "Auth Test");
      }
    } catch (e) {
      printty("❌ Auth state test error: $e", logName: "Auth Test");
    }
  }
  
  /// Tests making an authenticated API call
  static Future<void> testAuthenticatedApiCall() async {
    printty("=== Testing Authenticated API Call ===", logName: "Auth Test");
    
    try {
      // Make a test API call that requires authentication
      final response = await apiService.getWithAuth(url: "/customer");
      
      if (response.success) {
        printty("✅ Authenticated API call successful", logName: "Auth Test");
      } else {
        printty("❌ Authenticated API call failed: ${response.message}", logName: "Auth Test");
        printty("Response code: ${response.code}", logName: "Auth Test");
      }
    } catch (e) {
      printty("❌ Authenticated API call error: $e", logName: "Auth Test");
    }
  }
  
  /// Simulates a 401 error to test automatic token refresh
  static Future<void> simulateTokenExpiry() async {
    printty("=== Simulating Token Expiry ===", logName: "Auth Test");
    
    try {
      // Store current tokens
      final currentAccessToken = await StorageService.getAccessToken();
      final currentRefreshToken = await StorageService.getRefreshToken();
      
      if (currentAccessToken == null || currentRefreshToken == null) {
        printty("⚠️ No tokens to test with", logName: "Auth Test");
        return;
      }
      
      // Store an invalid access token to simulate expiry
      await StorageService.storeAccessToken("invalid_token_for_testing");
      printty("Stored invalid access token", logName: "Auth Test");
      
      // Make an API call that should trigger token refresh
      final response = await apiService.getWithAuth(url: "/customer");
      
      if (response.success) {
        printty("✅ Token refresh worked automatically", logName: "Auth Test");
      } else {
        printty("❌ Automatic token refresh failed", logName: "Auth Test");
        // Restore original tokens
        await StorageService.storeAccessToken(currentAccessToken);
      }
    } catch (e) {
      printty("❌ Token expiry simulation error: $e", logName: "Auth Test");
    }
  }
  
  /// Helper method to check if valid tokens exist
  static Future<bool> _hasValidTokens() async {
    final accessToken = await StorageService.getAccessToken();
    final refreshToken = await StorageService.getRefreshToken();
    return accessToken != null && refreshToken != null;
  }
  
  /// Runs all authentication tests
  static Future<void> runAllTests() async {
    printty("🚀 Starting Authentication Tests", logName: "Auth Test");
    
    await testAuthState();
    await Future.delayed(const Duration(seconds: 1));
    
    await testTokenRefresh();
    await Future.delayed(const Duration(seconds: 1));
    
    await testAuthenticatedApiCall();
    await Future.delayed(const Duration(seconds: 1));
    
    // Only run token expiry simulation if we have valid tokens
    final hasTokens = await _hasValidTokens();
    if (hasTokens) {
      await simulateTokenExpiry();
    }
    
    printty("✅ Authentication tests completed", logName: "Auth Test");
  }
}
