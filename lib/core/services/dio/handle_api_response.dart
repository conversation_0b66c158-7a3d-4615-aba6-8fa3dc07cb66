import 'package:bottle_king_mobile/core/core.dart';

/// Handles API response and shows appropriate toasts and executes callbacks
void handleApiResponse({
  required ApiResponse response,
  String? successMsg,
  String? errorMsg,
  bool showErrorMessage = true,
  bool showSuccessMessage = true,
  void Function()? onSuccess,
  void Function()? onError,
}) {
  if (response.success) {
    onSuccess?.call();

    if (showSuccessMessage) {
      showSuccessToastMessage(
          successMsg ?? response.message ?? 'Operation successful');
    }
  } else {
    onError?.call();
    if (showErrorMessage) {
      showWarningToast(errorMsg ?? response.message ?? 'Something went wrong');
    }
  }
}

/// Shows an error toast message
void showWarningToast(String msg) {
  FlushBarToast.fLSnackBar(message: msg);
}

/// Shows a success toast message
void showSuccessToastMessage(String msg) {
  FlushBarToast.fLSnackBar(
    message: msg,
    snackBarType: SnackBarType.success,
  );
}
