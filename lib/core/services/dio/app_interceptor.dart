import 'dart:async';

import 'package:bottle_king_mobile/core/core.dart';
import 'package:dio/dio.dart';

class AppInterceptors extends QueuedInterceptorsWrapper {
  final ProviderContainer? _container;

  AppInterceptors({ProviderContainer? container}) : _container = container;
  Dio dio = Dio();
  CancelToken cancelToken = CancelToken();
  bool isTrustedDevice = true;

  @override
  FutureOr<dynamic> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    String? token = await StorageService.getAccessToken();

    if (token != null) {
      options.headers.addAll({"authorization": "Bearer $token"});
    }
    handler.next(options);
  }

  @override
  FutureOr<dynamic> onResponse(
      Response response, ResponseInterceptorHandler handler) async {
    handler.next(response);
  }

  @override
  FutureOr<dynamic> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    ApiResponse res = DioResponseHandler.dioErrorHandler(err);

    // Handle 401 Unauthorized - attempt token refresh
    if (res.code == 401) {
      printty("401 Unauthorized - attempting token refresh",
          logName: "Interceptor");

      // Skip refresh for refresh endpoint to avoid infinite loop
      if (err.requestOptions.path.contains('/auth/refresh')) {
        printty("Refresh endpoint failed - logging out",
            logName: "Interceptor");
        await logout();
        return handler.reject(err);
      }

      // Attempt to refresh token
      final newToken = await _attemptTokenRefresh();

      if (newToken != null) {
        // Retry the original request with new token
        printty("Retrying request with new token", logName: "Interceptor");
        err.requestOptions.headers["authorization"] = "Bearer $newToken";
        err.requestOptions.headers["bk"] = "cGro2jdDtL4CFNSJ1UWzUeZUVjJkydwP";

        try {
          final response = await dio.fetch(err.requestOptions);
          return handler.resolve(response);
        } catch (e) {
          printty("Retry failed: $e", logName: "Interceptor");

          // TODO: handle this case.
          // await logout();
          return handler.reject(err);
        }
      } else {
        // Refresh failed, logout user
        printty("Token refresh failed - logging out", logName: "Interceptor");
        await logout();
        return handler.reject(err);
      }
    }

    // Handle 403 Forbidden
    if (res.code == 403) {
      await logout();
      return handler.reject(err);
    }

    if (res.code == 500 &&
        res.message != null &&
        res.message!
            .contains("This device is not set as your trusted device.")) {
      ApiResponse res = DioResponseHandler.dioErrorHandler(err);
      printty("ssss: ${res.message}");
      final dioError = DioException(
          message: res.message,
          type: DioExceptionType.badResponse,
          requestOptions: err.requestOptions,
          response: Response(
              statusCode: 409,
              requestOptions: err.requestOptions,
              data: {
                "success": false,
                "message": res.message ??
                    "This device is not set as your trusted device"
              },
              statusMessage: res.message ??
                  "This device is not set as your trusted device"));
      return handler.reject(dioError);
    }

    return handler.next(err);
  }

  /// Attempts to refresh the access token
  /// Returns the new access token if successful, null otherwise
  Future<String?> _attemptTokenRefresh() async {
    try {
      return await TokenRefreshService.refreshAccessToken();
    } catch (e) {
      printty("Error during token refresh: $e", logName: "Interceptor");
      return null;
    }
  }

  Future<void> logout() async {
    try {
      // Check if user is not null before logging out
      if (_container != null) {
        final authVmInstance = _container.read(authVm);
        if (authVmInstance.user == null) {
          printty("User is null, skipping logout", logName: "Interceptor");
          return;
        }
        // Set user to null when logging out
        authVmInstance.logout();
      } else {
        // Fallback: clear storage and navigate if container is not available
        TokenRefreshService.clearRefreshState();
        await StorageService.logout();
        gotoNextScreen(RoutePath.welcomeScreen);
      }
    } catch (e) {
      printty("Error during logout: $e", logName: "Interceptor");
      // Fallback: clear storage and navigate
      await StorageService.logout();
      gotoNextScreen(RoutePath.welcomeScreen);
    }
  }

  gotoNextScreen(String route) {
    if (NavKey.appNavKey.currentContext != null) {
      Navigator.pushNamedAndRemoveUntil(
          NavKey.appNavKey.currentContext!, route, (r) => false);
    }
  }
}
