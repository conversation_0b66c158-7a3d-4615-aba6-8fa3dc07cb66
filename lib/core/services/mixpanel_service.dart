import 'package:bottle_king_mobile/core/core.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

abstract class AnalyticsClient {
  Future<void> init(String token);
  Future<void> identify(String distinctId);
  Future<void> track(String eventName, [Map<String, dynamic>? properties]);
  Future<void> registerSuperProperties(Map<String, dynamic> properties);
  Future<void> setPeopleProperties(Map<String, dynamic> properties);
  Future<void> optOutTracking();
  Future<void> optInTracking();
  Future<void> reset();
  Future<void> flush();
}

class MixpanelAnalyticsClient implements AnalyticsClient {
  Mixpanel? _mixpanel;

  @override
  Future<void> init(String token) async {
    try {
      _mixpanel = await Mixpanel.init(token, trackAutomaticEvents: false);
      printty('Mixpanel initialized', logName: 'Analytics');
    } catch (e) {
      printty('Mixpanel initialization failed: $e', logName: 'Analytics');
      throw StateError('Mixpanel initialization failed: $e');
    }
  }

  Mixpanel _require() {
    final m = _mixpanel;
    if (m == null) {
      throw StateError('Mixpanel not initialized');
    }
    return m;
  }

  @override
  Future<void> identify(String distinctId) async {
    await _require().identify(distinctId);
  }

  @override
  Future<void> track(String eventName,
      [Map<String, dynamic>? properties]) async {
    await _require().track(eventName, properties: properties);
  }

  @override
  Future<void> registerSuperProperties(Map<String, dynamic> properties) async {
    await _require().registerSuperProperties(properties);
  }

  @override
  Future<void> setPeopleProperties(Map<String, dynamic> properties) async {
    final people = _require().getPeople();
    properties.forEach((key, value) {
      people.set("\$$key", value);
    });
  }

  @override
  Future<void> optOutTracking() async {
    _require().optOutTracking();
    return;
  }

  @override
  Future<void> optInTracking() async {
    _require().optInTracking();
    return;
  }

  @override
  Future<void> reset() async {
    await _require().reset();
  }

  @override
  Future<void> flush() async {
    await _require().flush();
  }
}

class AnalyticsService {
  AnalyticsService._();
  static final AnalyticsService instance = AnalyticsService._();

  AnalyticsClient _client = MixpanelAnalyticsClient();
  bool _initialized = false;

  void useClient(AnalyticsClient client) {
    _client = client;
  }

  Future<void> initialize() async {
    if (_initialized) return;
    try {
      final token = EnvConfig.mixpanelProjectToken;
      if (token.isEmpty) {
        printty('Mixpanel token missing', logName: 'Analytics');
        if (_client is MixpanelAnalyticsClient) {
          return;
        }
      } else {
        printty("Mixpanel token: $token", logName: 'Analytics');
        await _client.init(token);
      }
      _initialized = true;
      await registerSuperProperties({'platform': 'mobile'});
    } catch (e) {
      printty('Analytics init error: $e', logName: 'Analytics');
    }
  }

  Future<void> setTrackingEnabled(bool enabled) async {
    try {
      if (enabled) {
        await _client.optInTracking();
      } else {
        await _client.optOutTracking();
      }
    } catch (e) {
      printty('Analytics opt toggle error: $e', logName: 'Analytics');
    }
  }

  Future<void> identifyUser(AuthUserModel? user) async {
    if (_client is MixpanelAnalyticsClient) {
      if (!_initialized) return;
    }
    try {
      final id = user?.id;
      if (id != null && id.isNotEmpty) {
        await _client.identify(id);
        await _client.setPeopleProperties({
          'email': user?.email ?? '',
          'name': user?.firstname ?? '',
          'firstname': user?.firstname ?? '',
          'lastname': user?.lastname ?? '',
          'phone': user?.phone ?? '',
          'role': user?.roleType ?? '',
          'verified': user?.verified ?? false,
        });
      } else {
        printty('Analytics identify error: user id is empty',
            logName: 'Analytics');
      }
    } catch (e) {
      printty('Analytics identify error: $e', logName: 'Analytics');
    }
  }

  Future<void> reset() async {
    try {
      await _client.reset();
    } catch (e) {
      printty('Analytics reset error: $e', logName: 'Analytics');
    }
  }

  Future<void> track(String name, [Map<String, dynamic>? props]) async {
    if (_client is MixpanelAnalyticsClient) {
      if (!_initialized) return;
    }
    try {
      await _client.track(name, {
        ...?props,
        'platform': 'mobile',
      });
    } catch (e) {
      printty('Analytics track error: $e', logName: 'Analytics');
    }
  }

  Future<void> registerSuperProperties(Map<String, dynamic> props) async {
    if (_client is MixpanelAnalyticsClient) {
      if (!_initialized) return;
    }
    try {
      await _client.registerSuperProperties(props);
    } catch (e) {
      printty('Analytics super props error: $e', logName: 'Analytics');
    }
  }

  Future<void> trackLoginSuccess({required String method}) async {
    try {
      await track('login_success', {'method': method});
    } catch (e) {
      printty('Analytics track login_success error: $e', logName: 'Analytics');
    }
  }

  Future<void> trackLogout() async {
    await track('logout', {});
  }

  Future<void> trackProductView(
      {required String productId, String? name}) async {
    await track('product_view', {'product_id': productId, 'name': name});
  }

  Future<void> trackAddToCart({
    required String productId,
    String? variationId,
    int quantity = 1,
  }) async {
    await track('add_to_cart', {
      'product_id': productId,
      'variation_id': variationId,
      'quantity': quantity,
    });
  }

  Future<void> trackCheckoutStarted(
      {required String cartId, num? total}) async {
    await track('checkout_started', {'cart_id': cartId, 'total': total});
  }

  Future<void> trackOrderCompleted(
      {required String orderId, num? total}) async {
    await track('order_completed', {'order_id': orderId, 'total': total});
  }

  Future<void> flush() async {
    try {
      await _client.flush();
    } catch (e) {
      printty('Analytics flush error: $e', logName: 'Analytics');
    }
  }
}
