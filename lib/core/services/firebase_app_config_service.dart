import 'package:bottle_king_mobile/core/core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseAppConfigService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'app-config';

  /// Fetches the minimum app version configuration from Firestore
  /// Returns an AppConfigModel with minimum version requirements
  static Future<AppConfigModel?> getAppConfig() async {
    try {
      printty('Fetching app config from Firestore...');

      // Get the app-config collection
      final QuerySnapshot querySnapshot =
          await _firestore.collection(_collectionName).limit(1).get();

      if (querySnapshot.docs.isEmpty) {
        printty('No app config document found in Firestore');
        return null;
      }

      // Get the first document from the collection
      final DocumentSnapshot doc = querySnapshot.docs.first;
      final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      // Add document ID to the data
      data['_id'] = doc.id;

      printty('App config fetched successfully: $data');

      return AppConfigModel.fromJson(data);
    } catch (e) {
      printty('Error fetching app config from Firestore: $e');
      return null;
    }
  }

  /// Fetches app config by document ID
  /// Useful if you know the specific document ID
  static Future<AppConfigModel?> getAppConfigById(String documentId) async {
    try {
      printty('Fetching app config by ID: $documentId');

      final DocumentSnapshot doc =
          await _firestore.collection(_collectionName).doc(documentId).get();

      if (!doc.exists) {
        printty('App config document with ID $documentId not found');
        return null;
      }

      final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
      data['_id'] = doc.id;

      printty('App config fetched successfully by ID: $data');

      return AppConfigModel.fromJson(data);
    } catch (e) {
      printty('Error fetching app config by ID from Firestore: $e');
      return null;
    }
  }

  /// Listens to real-time updates of app config
  /// Returns a stream of AppConfigModel updates
  static Stream<AppConfigModel?> listenToAppConfig() {
    try {
      return _firestore
          .collection(_collectionName)
          .limit(1)
          .snapshots()
          .map((QuerySnapshot querySnapshot) {
        if (querySnapshot.docs.isEmpty) {
          printty('No app config document found in real-time listener');
          return null;
        }

        final DocumentSnapshot doc = querySnapshot.docs.first;
        final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['_id'] = doc.id;

        printty('App config updated in real-time: $data');

        return AppConfigModel.fromJson(data);
      });
    } catch (e) {
      printty('Error setting up app config listener: $e');
      return Stream.value(null);
    }
  }
}
