import 'package:bottle_king_mobile/core/core.dart';

/// Global provider container for accessing providers outside widgets
ProviderContainer? globalProviderContainer;

/// Initializes the API service
class ApiServiceInitializer {
  static void initialize({ProviderContainer? container}) {
    globalProviderContainer = container;
    apiService = DioApiService(
      appInterceptors: AppInterceptors(container: container),
    );
  }
}
