import 'package:shorebird_code_push/shorebird_code_push.dart';

/// A thin wrapper around <PERSON>bird's updater to expose
/// simple methods for checking and downloading patches.
class ShorebirdUpdaterService {
  ShorebirdUpdaterService._();
  static final ShorebirdUpdaterService instance = ShorebirdUpdaterService._();

  final ShorebirdUpdater _updater = ShorebirdUpdater();

  /// Returns the currently installed patch number, or null if none.
  Future<int?> getInstalledPatchNumber() async {
    final patch = await _updater.readCurrentPatch();
    return patch?.number;
  }

  /// Checks if a new patch is available on the given track.
  /// Defaults to stable track.
  Future<bool> isNewPatchAvailable({UpdateTrack? track}) async {
    final status = await _updater.checkForUpdate(track: track);
    return status == UpdateStatus.outdated;
  }

  /// Downloads and prepares a new patch if available.
  /// Returns true if a patch was downloaded.
  Future<bool> downloadNewPatch({UpdateTrack? track}) async {
    final status = await _updater.checkForUpdate(track: track);
    if (status == UpdateStatus.outdated) {
      await _updater.update(track: track);
      return true;
    }
    return false;
  }
}