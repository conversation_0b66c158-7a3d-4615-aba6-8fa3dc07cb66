import 'package:bottle_king_mobile/core/core.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';

class AppInitService {
  Future<void> init(Future<void> Function(RemoteMessage) fcmBackgroundHandler,
      {ProviderContainer? container}) async {
    // Initialize API service
    ApiServiceInitializer.initialize(container: container);

    //screen orientation
    await _screenOrientationInit();

    //hive
    await _initializeHive();

    //firebase
    await _firebaseInit();

    //local push notifications
    await LocalPushNotificationService.init();

    //fcm
    await FirebasePushNotificationService.init(fcmBackgroundHandler);

    // AppsFlyer deep link initialization
    await AppsFlyerService.instance.initialize();

    await AnalyticsService.instance.initialize();

    // Optionally check and download Shorebird patches in background
    _shorebirdAutoUpdate();
  }

  _firebaseInit() async {
    try {
      await Firebase.initializeApp();
      printty("===> firebase initialized...");
    } catch (e) {
      printty(e.toString(), logName: 'Firebase Error check');
    }
  }

  Future<void> _initializeHive() async {
    try {
      await Hive.initFlutter();
      await Hive.openBox(StorageKey.generalHiveBox);
      printty("===> hive initialized...");
    } catch (e) {
      printty(e.toString(), logName: 'Hive Init Error');
    }
  }

  _screenOrientationInit() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    printty("===> screen orientation initialized...");
  }

  // _requestPushNotificationPermission() async {
  //   final token = await FirebaseMessaging.instance.getToken();
  //   printty(token, logName: 'FireBASE-TOKEN');
  //   //await Firebase.initializeApp();
  //   FirebaseMessaging.instance.requestPermission(
  //       alert: true, badge: true, criticalAlert: true, sound: true);
  //   FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
  //       alert: true, badge: true, sound: true);
  // }

  /// Background Shorebird update check and download (no UI changes).
  void _shorebirdAutoUpdate() async {
    try {
      final hasUpdate =
          await ShorebirdUpdaterService.instance.isNewPatchAvailable();
      if (hasUpdate) {
        printty('Shorebird: update available, downloading...');
        final downloaded =
            await ShorebirdUpdaterService.instance.downloadNewPatch();
        if (downloaded) {
          printty('Shorebird: patch downloaded. Will apply on next restart.');
        }
      } else {
        printty('Shorebird: no update available');
      }
    } catch (e) {
      printty('Shorebird updater error: $e', logName: 'Shorebird');
    }
  }
}
