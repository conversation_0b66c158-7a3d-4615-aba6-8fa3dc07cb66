import 'dart:async';
import 'dart:io';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:bottle_king_mobile/core/core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// AppsFlyer deep link service
class AppsFlyerService {
  AppsFlyerService._();
  static final AppsFlyerService instance = AppsFlyerService._();

  late final AppsflyerSdk _afSdk;
  bool hasHandledInitialLink = false;
  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    final devKey = dotenv.env['AF_DEV_KEY'];
    final appId = dotenv.env['AF_APP_ID'];

    if (devKey == null || devKey.isEmpty) {
      printty('AppsFlyer dev key missing. Skipping init.',
          logName: 'AppsFlyer');
      return;
    }
    // On iOS, appId is required (App Store ID). Allow Android to initialize without it.
    if (Platform.isIOS && (appId == null || appId.isEmpty)) {
      printty('AppsFlyer iOS appId missing. Skipping init on iOS.',
          logName: 'AppsFlyer');
      return;
    }

    final afOptions = AppsFlyerOptions(
      afDevKey: devKey,
      appId: Platform.isIOS ? appId! : "",
      showDebug: true,
      timeToWaitForATTUserAuthorization: 10,
      // Set to true to allow more time for attribution. Adjust as needed.
      manualStart: false,
    );

    _afSdk = AppsflyerSdk(afOptions);

    // Subscribe to deep linking events (direct and deferred)
    _afSdk.onDeepLinking((DeepLinkResult result) {
      _handleDeepLinkResult(result);
    });

    try {
      await _afSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true,
      );
      _initialized = true;
      printty('AppsFlyer initialized', logName: 'AppsFlyer');
    } catch (e) {
      printty('AppsFlyer init error: $e', logName: 'AppsFlyer');
    }
  }

  Future<void> _handleDeepLinkResult(DeepLinkResult result) async {
    final status = result.status;
    final dl = result.deepLink;
    printty("Deepk=link result $result");

    // Try to parse path from URL if available
    String? rawPath = dl?.clickEvent["path"]?.toString();

    if (rawPath == null || rawPath.isEmpty) return;

    // Split and sanitize segments
    final parts = rawPath
        .split('/')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    // "/nmB9/ref/202020"
    final String second = parts[1];
    final String? third = parts.length > 2 ? parts[2] : null;

    // path = "/nmB9/ref/202020"
    if (second.contains("ref")) {
      final refCode = third;
      printty("refCode xx $refCode");
      if (refCode != null && refCode.isNotEmpty) {
        await StorageService.storeStringItem(StorageKey.referralCode, refCode);
        // No navigation for referral links.
      }
    }
  }
}

extension _IterableFirstOrNull<E> on Iterable<E> {
  E? get firstOrNull {
    final it = iterator;
    return it.moveNext() ? it.current : null;
  }
}
