// import 'package:bottle_king_mobile/core/core.dart';

// /// Extracts all variations from a list of products using the copyWith pattern
// /// for immutable data handling. This preserves all variation data while adding
// /// product context information.
// ///
// /// @param products - List of ProductModel objects
// /// @returns List of all Variations with productId and productName populated
// List<Variation> extractVariations(List<ProductModel> products) {
//   return products.fold<List<Variation>>([], (variations, product) {
//     // Skip products without variations to avoid null pointer exceptions
//     if (product.variations == null || product.variations!.isEmpty) {
//       return variations;
//     }

//     /**
//     |--------------------------------------------------
//     | Use copyWith to create immutable copies of variations
//     | with productId and productName added, preserving
//     | all existing variation data
//     |--------------------------------------------------
//     */
//     final updatedVariations = product.variations!
//         .map((variation) => variation.copyWith(
//               productId: product.id,
//               productName: "${product.name} ${variation.name ?? ""}",
//             ))
//         .toList();

//     /**
//     |--------------------------------------------------
//     | Return new list with existing variations and
//     | updated variations merged immutably
//     |--------------------------------------------------
//     */
//     return [...variations, ...updatedVariations];
//   });
// }
