class PhoneValidationResult {
  final bool isNumeric;
  final bool isValidLength;
  final bool isValid;
  final String sanitizedLocal;
  final String? errorMessage;

  const PhoneValidationResult({
    required this.isNumeric,
    required this.isValidLength,
    required this.isValid,
    required this.sanitizedLocal,
    this.errorMessage,
  });
}

class PhoneUtils {
  static const Map<String, (int min, int max)> _countryNsnRules = {
    'NG': (10, 10),
    'GH': (9, 9),
    'KE': (9, 9),
    'ZA': (9, 9),
    'EG': (10, 10),
    'GB': (10, 10),
    'DE': (10, 11),
    'FR': (9, 9),
    'IT': (9, 10),
    'ES': (9, 9),
    'US': (10, 10),
    'CA': (10, 10),
    'BR': (10, 11),
    'MX': (10, 10),
    'IN': (10, 10),
    'CN': (10, 11),
    'JP': (9, 10),
    'UK': (10, 10),
    'EU': (10, 10),
  };

  static String sanitizeNumeric(String input) {
    return input.replaceAll(RegExp(r'[^0-9]'), '');
  }

  static String removeLeadingZero(String local) {
    if (local.startsWith('0')) {
      return local.substring(1);
    }
    return local;
  }

  static PhoneValidationResult validateLocal({
    required String isoCountryCode,
    required String rawLocalInput,
  }) {
    final sanitized = sanitizeNumeric(rawLocalInput);
    final isNumeric =
        sanitized.isNotEmpty && RegExp(r'^\d+$').hasMatch(sanitized);

    if (sanitized.isEmpty) {
      return const PhoneValidationResult(
        isNumeric: false,
        isValidLength: false,
        isValid: false,
        sanitizedLocal: '',
        errorMessage: 'Phone number is required',
      );
    }

    final nsn = removeLeadingZero(sanitized);

    final rule = _countryNsnRules[isoCountryCode];
    final min = rule?.$1 ?? 7;
    final max = rule?.$2 ?? 15;

    final isValidLength = nsn.length >= min && nsn.length <= max;

    String? error;
    if (!isNumeric) {
      error = 'Only digits are allowed';
    } else if (!isValidLength) {
      error = 'Invalid length for $isoCountryCode: $min–$max digits expected';
    }

    return PhoneValidationResult(
      isNumeric: isNumeric,
      isValidLength: isValidLength,
      isValid: isNumeric && isValidLength,
      sanitizedLocal: nsn,
      errorMessage: error,
    );
  }

  static String formatE164({
    required String countryPhoneCode,
    required String rawLocalInput,
  }) {
    final sanitized = sanitizeNumeric(rawLocalInput);
    final nsn = removeLeadingZero(sanitized);
    return '+$countryPhoneCode$nsn';
  }

  static String formatInternationalDigits({
    required String countryPhoneCode,
    required String rawLocalInput,
  }) {
    final sanitized = sanitizeNumeric(rawLocalInput);
    final nsn = removeLeadingZero(sanitized);
    return '$countryPhoneCode$nsn';
  }

  static String? extractOtpCode(
    String smsBody, {
    List<int> candidateLengths = const [4, 6],
  }) {
    for (final len in candidateLengths) {
      final match = RegExp('\\b(\\d{$len})\\b').firstMatch(smsBody);
      if (match != null) {
        return match.group(1);
      }
    }
    final matches = RegExp('\\b(\\d{4,8})\\b').allMatches(smsBody).toList();
    if (matches.isNotEmpty) {
      matches.sort((a, b) => b.group(1)!.length.compareTo(a.group(1)!.length));
      return matches.first.group(1);
    }
    return null;
  }
}
