int extractDigits(String input) {
  // Use regular expression to find all digits in the string
  final digitMatches = RegExp(r'\d+').allMatches(input);

  // Combine all matched digits into a single string
  final digitsString = digitMatches.map((match) => match.group(0)).join();

  // Parse the combined digits string to an integer
  // Return 0 if no digits are found (could also return null or throw)
  return digitsString.isNotEmpty ? int.parse(digitsString) : 0;
}
