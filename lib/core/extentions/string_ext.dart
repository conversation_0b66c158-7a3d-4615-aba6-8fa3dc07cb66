extension StringExtension on String? {
  String capitalize() {
    if (this == null || this!.isEmpty) return this ?? '';

    // For single character strings
    if (this!.length == 1) return this!.toUpperCase();

    // For strings with multiple characters
    return '${this![0].toUpperCase()}${this!.substring(1).toLowerCase()}';
  }

  // Bonus: Title case for multiple words
  String capitalizeWords() {
    if (this == null || this!.isEmpty) return this ?? '';

    return this!
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ');
  }

  String initials() {
    if (this == null || this!.trim().isEmpty) return '';

    final List<String> names = this!.trim().split(' ');

    // Handle single name
    if (names.length == 1) {
      return names[0][0].toUpperCase();
    }

    // Get first letters of first and last names
    return '${names.first[0].toUpperCase()}${names.last[0].toUpperCase()}';
  }
}
