import 'package:bottle_king_mobile/core/core.dart';

class AppTypography {
  static TextStyle text8 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(8),
  );

  static TextStyle text10 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(10),
  );

  static TextStyle text12 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(12),
  );

  static TextStyle text13 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(13),
  );

  static TextStyle text14 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(14),
  );

  static TextStyle text15 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(15),
  );

  static TextStyle text16 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(16),
  );

  static TextStyle text18 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(18),
  );

  static TextStyle text20 = TextStyle(
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(20),
  );

  static TextStyle text24 = TextStyle(
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(24),
  );

  static TextStyle text26 = TextStyle(
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(26),
  );

  static TextStyle text28 = TextStyle(
    color: AppColors.primaryBlack,
    fontSize: Sizer.text(28),
  );

  static TextStyle text30 = TextStyle(
    color: AppColors.primaryBlack,
    height: 1.2,
    fontSize: Sizer.text(30),
  );

  static TextStyle text32 = TextStyle(
    color: AppColors.primaryBlack,
    height: 1.2,
    fontSize: Sizer.text(32),
  );

  static TextStyle text36 = TextStyle(
    color: AppColors.primaryBlack,
    height: 1.2,
    fontSize: Sizer.text(36),
  );

  static TextStyle text48 = TextStyle(
    color: AppColors.primaryBlack,
    height: 1.2,
    fontSize: Sizer.text(48),
  );
}
