import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/screens/screen.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    final args = settings.arguments;
    final requestedName = settings.name ?? RoutePath.splashScreen;
    final navState = NavKey.appNavKey.currentState;

    // Guard: prevent automatic routing for AppsFlyer OneLink referral paths
    // Example incoming path: "/nmB9/ref/202020". We show splash and let
    // normal app launch flow proceed without redirection.
    if (requestedName.startsWith('/nmB9/') || requestedName.contains('/ref/')) {
      // If app is already running, push a transparent no-op route that
      // doesn’t change the current screen. On cold start, show splash.
      printty("requestedName: $requestedName");
      // if (navState != null) {
      //   return PageRouteBuilder(
      //     settings: const RouteSettings(name: 'noop-referral'),
      //     opaque: false,
      //     barrierColor: null,
      //     transitionDuration: Duration.zero,
      //     reverseTransitionDuration: Duration.zero,
      //     pageBuilder: (_, __, ___) => const SizedBox.shrink(),
      //   );
      // }
      return TransitionUtils.buildTransition(
        const SplashScreen(),
        const RouteSettings(name: RoutePath.splashScreen),
      );
    }

    switch (requestedName) {
      case RoutePath.splashScreen:
        return TransitionUtils.buildTransition(
          const SplashScreen(),
          settings,
        );

      case RoutePath.onboardingScreen:
        return TransitionUtils.buildTransition(
          const OnboardingScreen(),
          settings,
        );

      case RoutePath.bottomNavScreen:
        final arg = args as DashArg?;
        return TransitionUtils.buildTransition(
          BottomNavScreen(args: arg),
          settings,
        );

      case RoutePath.forceUpdateScreen:
        return TransitionUtils.buildTransition(
          const ForceUpdateScreen(),
          settings,
        );

      // Auth
      case RoutePath.welcomeScreen:
        return TransitionUtils.buildTransition(
          const WelcomeBackScreen(),
          settings,
        );

      case RoutePath.registerScreen:
        return TransitionUtils.buildTransition(
          const RegisterScreen(),
          settings,
        );

      case RoutePath.loginScreen:
        return TransitionUtils.buildTransition(
          const LoginScreen(),
          settings,
        );

      case RoutePath.otpScreen:
        if (args is OtpArg) {
          return TransitionUtils.buildTransition(
            OtpScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Shop
      case RoutePath.shopScreen:
        return TransitionUtils.buildTransition(
          const ShopScreen(),
          settings,
        );

      case RoutePath.shopProductScreen:
        if (args is String) {
          return TransitionUtils.buildTransition(
            ShopProductScreen(category: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.productDetailsScreen:
        if (args is ProductModel) {
          return TransitionUtils.buildTransition(
            ProductDetailsScreen(product: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.searchProductScreen:
        final query = args as String?;
        return TransitionUtils.buildTransition(
          SearchProductScreen(query: query),
          settings,
        );

      // Cart
      case RoutePath.cartScreen:
        return TransitionUtils.buildTransition(
          const CartScreen(),
          settings,
        );

      case RoutePath.checkoutScreen:
        if (args is CartModel) {
          return TransitionUtils.buildTransition(
            CheckoutScreen(cartModel: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.payWithBottlekingScreen:
        return TransitionUtils.buildTransition(
          const PayWithBottlekingScreen(),
          settings,
        );

      // Orders
      case RoutePath.orderScreen:
        return TransitionUtils.buildTransition(
          const OrderScreen(),
          settings,
        );

      case RoutePath.orderDetailsScreen:
        if (args is OrderDetailsArg) {
          return TransitionUtils.buildTransition(
            OrderDetailsScreen(orderDetailsArg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.orderSuccessScreen:
        return TransitionUtils.buildTransition(
          const OrderSuccessScreen(),
          settings,
        );

      case RoutePath.csatScreen:
        final arg = args as CsatArg?;
        return TransitionUtils.buildTransition(
          CsatScreen(arg: arg),
          settings,
        );

      case RoutePath.receiptScreen:
        if (args is OrderDetailsModel) {
          return TransitionUtils.buildTransition(
            ReceiptScreen(order: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Address
      case RoutePath.addressScreen:
        return TransitionUtils.buildTransition(
          const AddressScreen(),
          settings,
        );

      case RoutePath.addAddressScreen:
        final showPreAddress = args != null ? args as bool : false;
        return TransitionUtils.buildTransition(
          AddAddressScreen(showPreAddress: showPreAddress),
          settings,
        );

      case RoutePath.noAddressScreen:
        return TransitionUtils.buildTransition(
          const NoAddressScreen(),
          settings,
        );

      case RoutePath.allAddressScreen:
        final arg = args != null ? args as bool : false;
        return TransitionUtils.buildTransition(
          AllAddressScreen(isHomeAddressChange: arg),
          settings,
        );

      // Profile
      case RoutePath.profileScreen:
        return TransitionUtils.buildTransition(
          const ProfileScreen(),
          settings,
        );

      case RoutePath.editProfileScreen:
        return TransitionUtils.buildTransition(
          const EditProfileScreen(),
          settings,
        );

      case RoutePath.deleteAccountScreen:
        return TransitionUtils.buildTransition(
          const DeleteAccountScreen(),
          settings,
        );

      case RoutePath.wishlistScreen:
        return TransitionUtils.buildTransition(
          const WishlistScreen(),
          settings,
        );

      case RoutePath.referralScreen:
        return TransitionUtils.buildTransition(
          const ReferralScreen(),
          settings,
        );

      case RoutePath.notificationScreen:
        return TransitionUtils.buildTransition(
          const NotificationScreen(),
          settings,
        );

      case RoutePath.walletScreen:
        return TransitionUtils.buildTransition(
          const WalletScreen(),
          settings,
        );

      case RoutePath.supportScreen:
        final arg = args as int?;
        return TransitionUtils.buildTransition(
          SupportScreen(tabIndex: arg),
          settings,
        );

      // Group order
      case RoutePath.groupOrderScreen:
        return TransitionUtils.buildTransition(
          const GroupOrderScreen(),
          settings,
        );

      case RoutePath.createGroupOrderScreen:
        return TransitionUtils.buildTransition(
          const CreateGroupOrderScreen(),
          settings,
        );

      case RoutePath.groupOrderShopScreen:
        return TransitionUtils.buildTransition(
          const GroupOrderShopScreen(),
          settings,
        );

      // Webview
      case RoutePath.customWebViewScreen:
        if (args is WebViewArg) {
          return TransitionUtils.buildTransition(
            CustomWebviewScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      default:
        return errorScreen(settings);
    }
  }

  static errorScreen(RouteSettings settings) {
    return TransitionUtils.buildTransition(
      ScreenNotFound(routeName: settings.name),
      settings,
    );
  }
}
