import 'package:flutter/material.dart';

// Type aliases to match the original skeletonizer API
typedef Skeletonizer = TempSkeletonizer;
typedef Bone = TempBone;

/// Temporary replacement for Skeletonizer widget
/// This is a simple shimmer effect implementation to replace the skeletonizer package
/// until it's updated to be compatible with the current Flutter version
class TempSkeletonizer extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const TempSkeletonizer({
    super.key,
    required this.child,
    this.enabled = true,
  });

  @override
  State<TempSkeletonizer> createState() => _TempSkeletonizerState();
}

class _TempSkeletonizerState extends State<TempSkeletonizer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.enabled) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(TempSkeletonizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.enabled != oldWidget.enabled) {
      if (widget.enabled) {
        _animationController.repeat();
      } else {
        _animationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.grey.shade200,
                Colors.white,
                Colors.grey.shade200,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ).createShader(bounds);
          },
          blendMode: BlendMode.srcATop,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
            ),
            child: Opacity(
              opacity: 0.0, // Hide the original content while preserving layout
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

/// Temporary replacement for Bone widget
class TempBone extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final bool expand;

  const TempBone({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.expand = false,
  });

  /// Icon constructor to match original API
  const TempBone.icon({
    super.key,
    double? size,
  })  : width = size ?? 24,
        height = size ?? 24,
        borderRadius = const BorderRadius.all(Radius.circular(12)),
        expand = false;

  /// Text constructor for text-like bones
  const TempBone.text({
    super.key,
    this.width,
    double? height,
  })  : height = height ?? 16,
        borderRadius = const BorderRadius.all(Radius.circular(4)),
        expand = false;

  /// Square constructor for square bones
  const TempBone.square({
    super.key,
    double? size,
  })  : width = size ?? 24,
        height = size ?? 24,
        borderRadius = const BorderRadius.all(Radius.circular(4)),
        expand = false;

  @override
  Widget build(BuildContext context) {
    Widget bone = Container(
      width: expand ? double.infinity : width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
    );

    if (expand && width == null) {
      return Expanded(child: bone);
    }

    return bone;
  }
}

// /// Temporary replacement for Skeleton class
// class Skeleton {
//   /// Replace method to match original API
//   static Widget replace({
//     required Widget replacement,
//     required Widget child,
//   }) {
//     return replacement;
//   }
// }
