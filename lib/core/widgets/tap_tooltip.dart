import 'dart:async';

import 'package:bottle_king_mobile/core/core.dart';

/// A lightweight tooltip that shows on tap and positions relative to the child.
/// Defaults to displaying above-right of the anchor.
class TapTooltip extends StatefulWidget {
  const TapTooltip({
    super.key,
    required this.child,
    required this.message,
    this.showDuration = const Duration(seconds: 3),
    this.padding,
    this.decoration,
    this.textStyle,
    this.maxWidth,
    this.offset,
  });

  final Widget child;
  final String message;
  final Duration showDuration;
  final EdgeInsetsGeometry? padding;
  final BoxDecoration? decoration;
  final TextStyle? textStyle;
  final double? maxWidth;

  /// Additional offset from the default top-right placement
  final Offset? offset;

  @override
  State<TapTooltip> createState() => _TapTooltipState();
}

class _TapTooltipState extends State<TapTooltip> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  Timer? _timer;

  @override
  void dispose() {
    _hideTooltip();
    super.dispose();
  }

  void _showTooltip() {
    if (_overlayEntry != null) return; // already showing

    _overlayEntry = OverlayEntry(
      builder: (context) {
        return CompositedTransformFollower(
          link: _layerLink,
          // Place the tooltip above-right of the target
          targetAnchor: Alignment.topRight,
          followerAnchor: Alignment.bottomRight,
          offset: (widget.offset ?? const Offset(0, 0)) +
              Offset(0, -Sizer.height(8)),
          child: Material(
            color: Colors.transparent,
            child: Container(
              constraints:
                  BoxConstraints(maxWidth: widget.maxWidth ?? Sizer.width(300)),
              padding: widget.padding ??
                  EdgeInsets.symmetric(
                    horizontal: Sizer.width(12),
                    vertical: Sizer.height(8),
                  ),
              decoration: widget.decoration ??
                  BoxDecoration(
                    color: AppColors.primaryBlack,
                    borderRadius: BorderRadius.circular(Sizer.radius(12)),
                  ),
              child: Text(
                widget.message,
                textAlign: TextAlign.left,
                style: widget.textStyle ??
                    AppTypography.text14.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ),
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
    _timer = Timer(widget.showDuration, _hideTooltip);
  }

  void _hideTooltip() {
    _timer?.cancel();
    _timer = null;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: InkWell(
        onTap: () {
          if (_overlayEntry != null) {
            _hideTooltip();
          } else {
            _showTooltip();
          }
        },
        child: widget.child,
      ),
    );
  }
}
