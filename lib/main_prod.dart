import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/my_app.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

// Listen to background messages
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  printty('Handling a background message ${message.messageId}');
  FirebasePushNotificationService.firebaseMessagingBackgroundHandler(message);
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize environment configuration
  await EnvConfig.initialize("prod");
  
  // Create provider container for global access
  final container = ProviderContainer();
  
  await AppInitService().init(_firebaseMessagingBackgroundHandler, container: container);
  runApp(UncontrolledProviderScope(container: container, child: const MyApp()));
}
