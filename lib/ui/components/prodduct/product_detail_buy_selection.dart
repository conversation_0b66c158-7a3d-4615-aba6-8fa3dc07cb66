import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ProductDetailBuySelection extends StatelessWidget {
  const ProductDetailBuySelection({
    super.key,
    required this.text,
    required this.amount,
    this.disCountPrice,
    this.isSelected = false,
    this.showDiscount = false,
    this.discountText,
    this.isCaseDiscount = false,
    this.onTap,
  });

  final String text;
  final String amount;
  final num? disCountPrice;
  final bool isSelected;
  final bool showDiscount;
  final String? discountText;
  final bool isCaseDiscount;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(
          Sizer.radius(16),
        ),
        decoration: BoxDecoration(
            border: Border.all(
          color: AppColors.blackBD,
        )),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  CustomRadioBtn(
                    isSelected: isSelected,
                    // onTap: () {},
                  ),
                  const XBox(8),
                  Text(
                    text,
                    style: AppTypography.text14.copyWith(),
                  ),
                  const XBox(12),
                  if (showDiscount && discountText != null)
                    DiscountTag(
                      text: discountText ?? "",
                      bgColor:
                          isCaseDiscount ? AppColors.green21 : AppColors.red35,
                    ),
                ],
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  amount,
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryBlack,
                    fontWeight: FontWeight.w500,
                    decoration: showDiscount
                        ? TextDecoration.lineThrough
                        : TextDecoration.none,
                  ),
                ),
                if (showDiscount && disCountPrice != null)
                  Text(
                    "${AppUtils.nairaSymbol}${(AppUtils.formatNumber(number: disCountPrice ?? 0))}",
                    style: AppTypography.text18.copyWith(
                      color:
                          isCaseDiscount ? AppColors.green21 : AppColors.red35,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
