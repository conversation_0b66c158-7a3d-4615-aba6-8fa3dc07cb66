import 'package:bottle_king_mobile/core/core.dart';

class DiscountTag extends StatelessWidget {
  const DiscountTag({
    super.key,
    required this.text,
    this.width,
    this.bgColor,
  });

  final String text;
  final double? width;
  final Color? bgColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(
        vertical: Sizer.radius(width != null ? 9 : 6),
        horizontal: Sizer.width(14),
      ),
      color: bgColor ?? AppColors.red2F,
      child: Center(
        child: Text(
          text,
          style: AppTypography.text12.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
