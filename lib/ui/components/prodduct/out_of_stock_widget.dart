import 'package:bottle_king_mobile/core/core.dart';

class OutOfStockWidget extends StatelessWidget {
  const OutOfStockWidget({
    super.key,
    this.width,
  });

  final double? width;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.radius(8),
        vertical: Sizer.height(width != null ? 10 : 4),
      ),
      margin: EdgeInsets.only(
        bottom: Sizer.height(4),
      ),
      color: AppColors.blackBD,
      child: Center(
        child: Text(
          "OUT OF STOCK",
          style: AppTypography.text10.copyWith(
            fontSize: width != null ? Sizer.text(12) : null,
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
