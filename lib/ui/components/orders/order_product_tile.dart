import 'package:bottle_king_mobile/core/core.dart';

class OrderProductTile extends StatelessWidget {
  const OrderProductTile({
    super.key,
    required this.imageUrl,
    required this.productName,
    required this.quantity,
    required this.price,
  });

  final String imageUrl;
  final String productName;
  final int quantity;
  final num price;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(10),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(2)),
            width: Sizer.width(45),
            height: Sizer.height(45),
            decoration: const BoxDecoration(
              color: AppColors.greyF7,
            ),
            child: MyCachedNetworkImage(
              imageUrl: imageUrl,
            ),
          ),
          const XBox(8),
          if (quantity > 1)
            Text(
              "${quantity}x",
              style: AppTypography.text14.copyWith(fontWeight: FontWeight.w600),
            ),
          const XBox(8),
          Expanded(
            child: Text(
              productName,
              style: AppTypography.text14.copyWith(),
            ),
          ),
          Text(
            '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: price)}',
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
