import 'package:bottle_king_mobile/core/core.dart';
import 'package:flutter/cupertino.dart';

import '../../components.dart';

class OngoingTab extends ConsumerStatefulWidget {
  const OngoingTab({
    super.key,
  });

  @override
  ConsumerState<OngoingTab> createState() => _OngoingTabState();
}

class _OngoingTabState extends ConsumerState<OngoingTab> {
  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    super.initState();
    final orderVm = ref.read(orderViewModel);
    _scrollController.addListener(() {
      if (_scrollController.position.maxScrollExtent ==
          _scrollController.offset) {
        if (orderVm.totalNumber >= orderVm.limit) {
          ref.read(orderViewModel.notifier).getOrdersList(
                isFirstCall: false,
                status: "pending",
              );
          printty("Paginating");
        } else {
          printty("No more data");
        }
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(orderViewModel.notifier).getOrdersList(status: "pending");
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orderRef = ref.watch(orderViewModel);
    return LoadableContentBuilder(
      isBusy: orderRef.busy(getState),
      items: orderRef.orderList,
      loadingBuilder: (context) {
        return const SizerLoader(height: 600);
      },
      emptyBuilder: (context) {
        return EmptyState(
          text: " No orders found",
          btnText: "Start shopping",
          onTap: () {
            Navigator.pushNamed(
              context,
              RoutePath.bottomNavScreen,
              arguments: DashArg(index: 1),
            );
          },
        );
      },
      contentBuilder: (context) {
        return RefreshIndicator(
          onRefresh: () async {
            ref.read(orderViewModel.notifier).getOrdersList(status: "pending");
          },
          child: ListView(
            controller: _scrollController,
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              top: Sizer.width(16),
              bottom: Sizer.width(100),
            ),
            children: [
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (ctx, i) {
                  final o = orderRef.orderList[i];
                  return OrderProductCard(order: o);
                },
                separatorBuilder: (_, __) => Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: Sizer.height(6),
                  ),
                  child: const Divider(color: AppColors.grayE6),
                ),
                itemCount: orderRef.orderList.length,
              ),
              if (orderRef.busy(paginateState))
                SizedBox(
                  height: Sizer.height(100),
                  child: const Center(
                    child: CupertinoActivityIndicator(),
                  ),
                )
            ],
          ),
        );
      },
    );
  }
}
