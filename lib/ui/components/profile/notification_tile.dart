import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class NotificationTile extends StatelessWidget {
  const NotificationTile({
    super.key,
    required this.title,
    required this.subtitle,
    this.isActive = false,
    required this.onChanged,
  });

  final String title;
  final String subtitle;
  final bool isActive;
  final Function(bool) onChanged;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const YBox(4),
              Text(
                subtitle,
                style: AppTypography.text13.copyWith(
                  color: AppColors.black70,
                ),
              )
            ],
          ),
        ),
        const XBox(26),
        CustomSwitch(value: isActive, onChanged: onChanged),
      ],
    );
  }
}
