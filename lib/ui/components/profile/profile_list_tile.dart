import 'package:bottle_king_mobile/core/core.dart';

class ProfileListTile extends StatelessWidget {
  const ProfileListTile({
    super.key,
    required this.title,
    required this.leadIconPath,
    this.isComingSoon = false,
    this.onTap,
  });

  final String title;
  final String leadIconPath;
  final bool isComingSoon;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isComingSoon ? null : onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Row(
          children: [
            SvgPicture.asset(leadIconPath),
            const XBox(12),
            Expanded(
              child: Text(
                title,
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (isComingSoon)
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.radius(8),
                  vertical: Sizer.radius(2),
                ),
                margin: EdgeInsets.only(right: Sizer.width(20)),
                decoration: BoxDecoration(
                  color: AppColors.red00,
                  borderRadius: BorderRadius.circular(Sizer.radius(10)),
                ),
                child: Text(
                  "Coming soon",
                  style: AppTypography.text12.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            Icon(
              Icons.arrow_forward_ios,
              size: Sizer.height(16),
              color: AppColors.black70,
            ),
          ],
        ),
      ),
    );
  }
}
