import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:card_swiper/card_swiper.dart';

class HomeSlider extends ConsumerStatefulWidget {
  const HomeSlider({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeSliderState();
}

class _HomeSliderState extends ConsumerState<HomeSlider> {
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   ref.read(sliderVmodel.notifier).getSliders();
    // });
  }

  List<SliderObject> get sliders =>
      ref.watch(appConfigVmodel).configData?.sliders ?? [];

  @override
  Widget build(BuildContext context) {
    return LoadableContentBuilder(
      isBusy: false,
      isError: false,
      items: sliders,
      loadingBuilder: (context) {
        return Padding(
          padding: EdgeInsets.all(Sizer.width(8)),
          child: Skeletonizer(
              child: Bone(
            height: Sizer.height(200),
            width: Sizer.screenWidth,
          )),
        );
      },
      errorBuilder: (p0) {
        return SizedBox(
          height: Sizer.height(200),
          child: Swiper(
            autoplay: true,
            autoplayDisableOnInteraction: true,
            autoplayDelay: 5000,
            duration: 1000,
            pagination: const SwiperPagination(
              alignment: Alignment.bottomCenter,
              builder: DotSwiperPaginationBuilder(
                color: Colors.white54,
                activeColor: AppColors.black,
                size: 8.0,
                activeSize: 10.0,
              ),
            ),
            onIndexChanged: (i) {
              currentIndex = i;
              setState(() {});
            },
            itemCount: 3,
            itemBuilder: (ctx, i) {
              return imageHelper(
                "assets/images/onboard/v$i.png",
                fit: BoxFit.cover,
              );
            },
          ),
        );
      },
      emptyBuilder: (ctx) => const SizedBox.shrink(),
      contentBuilder: (context) {
        return SizedBox(
          height: Sizer.height(200),
          child: Swiper(
            autoplay: true,
            autoplayDisableOnInteraction: true,
            autoplayDelay: 5000,
            duration: 1000,
            pagination: const SwiperPagination(
              alignment: Alignment.bottomCenter,
              builder: DotSwiperPaginationBuilder(
                color: Colors.white54,
                activeColor: AppColors.black,
                size: 8.0,
                activeSize: 10.0,
              ),
            ),
            onIndexChanged: (i) {
              currentIndex = i;
              setState(() {});
            },
            itemCount: sliders.length,
            itemBuilder: (ctx, i) {
              return InkWell(
                onTap: () {
                  final slider = sliders[i];
                  final routePath = switch (slider.type) {
                    search => RoutePath.searchProductScreen,
                    category => RoutePath.shopProductScreen,
                    subCategory => RoutePath.shopProductScreen,
                    product => RoutePath.productDetailsScreen,
                    _ => null,
                  };

                  if (routePath != null) {
                    if (slider.type == product) {
                      _getProductBySearch(slider.value ?? '').then((value) {
                        if (value != null) {
                          Navigator.pushNamed(
                            context,
                            routePath,
                            arguments: value,
                          );
                        }
                      });
                    } else {
                      Navigator.pushNamed(
                        context,
                        routePath,
                        arguments: slider.value,
                      );
                    }
                  }
                },
                child: MyCachedNetworkImage(
                  imageUrl: sliders[i].image ?? '',
                  fit: BoxFit.fill,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Future<ProductModel?> _getProductBySearch(String slug) async {
    final s = slug.trim();
    if (s.isEmpty) return null;

    try {
      final query = AppUtils.slugToSearchQuery(s);
      final res = await ref.read(productVm.notifier).productsBySearch(query: query);
      final items = res.data ?? const <ProductModel>[];
      if (res.success && items.isNotEmpty) {
        final lowerSlug = s.toLowerCase();
        // Prefer exact slug match
        final exact = items.firstWhere(
          (p) => (p.slug ?? '').toLowerCase() == lowerSlug,
          orElse: () => items.first,
        );
        return exact;
      }
    } catch (_) {}
    return null;
  }
}


 // return SizedBox(
//   height: Sizer.height(200),
//   child: Swiper(
//     autoplay: true,
//     autoplayDisableOnInteraction: true,
//     autoplayDelay: 5000,
//     duration: 1000,
//     // physics: const NeverScrollableScrollPhysics(),
//     onIndexChanged: (i) {
//       // currentIndex = i;
//       setState(() {});
//     },
//     itemCount: 3,
//     itemBuilder: (ctx, i) {
//       return imageHelper(
//         "assets/images/onboard/v$i.png",
//         fit: BoxFit.cover,
//       );
//     },
//   ),
// );