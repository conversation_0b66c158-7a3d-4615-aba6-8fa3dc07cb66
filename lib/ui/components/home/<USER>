import 'package:bottle_king_mobile/core/core.dart';

class HomeTab extends StatelessWidget {
  const HomeTab({
    super.key,
    required this.text,
    this.isSelected = false,
    this.margin,
    this.onTap,
  });

  final String text;
  final bool isSelected;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.height(4),
        ),
        margin: margin,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlack : AppColors.white,
          borderRadius: BorderRadius.circular(Sizer.radius(50)),
        ),
        child: Text(
          text,
          style: AppTypography.text12.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? AppColors.white : AppColors.primaryBlack,
          ),
        ),
      ),
    );
  }
}

class HomeTabOutline extends StatelessWidget {
  const HomeTabOutline({
    super.key,
    required this.text,
    this.isSelected = false,
    this.margin,
    this.onTap,
  });

  final String text;
  final bool isSelected;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.only(
          left: Sizer.width(2),
          right: Sizer.width(2),
          bottom: Sizer.height(2),
        ),
        margin: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1.5,
              color: isSelected ? AppColors.primaryBlack : Colors.transparent,
            ),
          ),
        ),
        child: Text(
          text,
          style: AppTypography.text13.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? AppColors.primaryBlack : AppColors.black70,
          ),
        ),
      ),
    );
  }
}
