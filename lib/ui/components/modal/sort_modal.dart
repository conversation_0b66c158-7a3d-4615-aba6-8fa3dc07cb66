import 'package:bottle_king_mobile/core/core.dart';

class SortModal extends StatefulWidget {
  const SortModal({
    super.key,
    required this.onConfirm,
    this.isBusy = false,
  });

  final VoidCallback onConfirm;
  final bool isBusy;

  @override
  State<SortModal> createState() => _SortModalState();
}

class _SortModalState extends State<SortModal> {
  final Map<String, String> _extraCategoryMap = {
    "Best sellers": "bestsellers",
    "Recommended": "recommended",
    "New arrivals": "newarrival",
    "Discount": "discount",
  };
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...List.generate(
                _extraCategoryMap.length,
                (i) {
                  final c = _extraCategoryMap.entries.toList()[i];
                  return SortListTile(
                    title: c.key,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  );
                },
              ),
              SortListTile(
                title: "Offers",
                onTap: () {},
              ),
              SortListTile(
                title: "Price low to high",
                onTap: () {},
              ),
              SortListTile(
                title: "Price high to low",
                onTap: () {},
              ),
            ],
          ),
          const YBox(40),
        ],
      ),
    );
  }
}

class SortListTile extends StatelessWidget {
  const SortListTile({
    super.key,
    required this.title,
    this.onTap,
  });

  final String title;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(
          bottom: Sizer.height(10),
        ),
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(10),
        ),
        child: Text(
          title,
          style: AppTypography.text14,
        ),
      ),
    );
  }
}
