import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ConfirmModal extends StatefulWidget {
  const ConfirmModal({
    super.key,
    required this.title,
    required this.subtitle,
    required this.rightBtnText,
    this.rightBtnTap,
    this.isLoading = false,
  });

  final String title;
  final String subtitle;
  final String rightBtnText;
  final VoidCallback? rightBtnTap;
  final bool isLoading;

  @override
  State<ConfirmModal> createState() => _ConfirmModalState();
}

class _ConfirmModalState extends State<ConfirmModal> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: Sizer.width(20),
        right: Sizer.width(20),
        top: Sizer.height(10),
        bottom: Sizer.height(20),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          Icon(
            Iconsax.information,
            size: Sizer.height(60),
            // color: AppColors.gray500,
          ),
          const YBox(24),
          Text(
            widget.title,
            textAlign: TextAlign.center,
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(4),
          Text(
            widget.subtitle,
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.grey,
            ),
          ),
          const YBox(32),
          widget.isLoading
              ? const BtnLoadState()
              : Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        text: "Close",
                        isOutline: true,
                        textColor: AppColors.primaryBlack,
                        online: true,
                        onTap: () {
                          Navigator.pop(context);
                        },
                      ),
                    ),
                    const XBox(12),
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: widget.rightBtnTap,
                        text: widget.rightBtnText,
                      ),
                    ),
                  ],
                ),
          const YBox(16),
        ],
      ),
    );
  }
}
