import 'package:bottle_king_mobile/core/core.dart';

class GroupOrderInfoModal extends ConsumerStatefulWidget {
  const GroupOrderInfoModal({super.key});

  @override
  ConsumerState<GroupOrderInfoModal> createState() =>
      _GroupOrderInfoModalState();
}

class _GroupOrderInfoModalState extends ConsumerState<GroupOrderInfoModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            children: [
              Text(
                "Group order info",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(20),
          _columnText("Group name", "Ralu’s group order"),
          _columnText("Date created", "01 May, 2025"),
          _columnText("Expiration date", "01 May, 2025"),
          const YBox(30),
        ],
      ),
    );
  }

  Widget _columnText(String title, String value) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: Sizer.height(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTypography.text14.copyWith(
              color: AppColors.black70,
            ),
          ),
          const YBox(4),
          Text(
            value,
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
