import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CreateGroupOrderModal extends ConsumerStatefulWidget {
  const CreateGroupOrderModal({super.key});

  @override
  ConsumerState<CreateGroupOrderModal> createState() =>
      _CreateGroupOrderModalState();
}

class _CreateGroupOrderModalState extends ConsumerState<CreateGroupOrderModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            children: [
              Text(
                "Create group order",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(24),
          CustomTextField(
            // controller: emailC,
            // focusNode: emailF,
            labelText: "Group order name ",
            showLabelHeader: true,
            borderRadius: 0,

            onChanged: (p0) => setState(() {}),
          ),
          const YBox(20),
          CustomTextField(
            // controller: emailC,
            // focusNode: emailF,
            labelText: "Set order timeline",
            showLabelHeader: true,
            borderRadius: 0,

            onChanged: (p0) => setState(() {}),
          ),
          const YBox(20),
          CustomTextField(
            // controller: emailC,
            // focusNode: emailF,
            labelText: "Choose payment method",
            showLabelHeader: true,
            borderRadius: 0,

            onChanged: (p0) => setState(() {}),
          ),
          const YBox(30),
          CustomBtn.solid(
            onTap: () async {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            // online: formValid,
            text: "Create group",
          ),
          const YBox(30),
        ],
      ),
    );
  }
}
