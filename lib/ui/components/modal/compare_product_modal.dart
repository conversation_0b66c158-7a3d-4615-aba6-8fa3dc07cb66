import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CompareProductModal extends ConsumerStatefulWidget {
  const CompareProductModal({super.key});

  @override
  ConsumerState<CompareProductModal> createState() =>
      _CompareProductModalState();
}

class _CompareProductModalState extends ConsumerState<CompareProductModal> {
  int _categoryIndex = -1;
  final List<String> _selectedCategories = [
    "Price",
    "Category",
    "Alcohol content",
    "Brand",
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productVm).getProducts(args: {
        "isMobile": "true",
        "extra_category": "bestsellers",
      });
    });
  }

  int selectedIndex = 0;
  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return Container(
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Row(
              children: [
                Text(
                  "Compare product",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  child: SvgPicture.asset(AppSvgs.close),
                ),
              ],
            ),
          ),
          const YBox(24),
          // SingleChildScrollView(
          //   scrollDirection: Axis.horizontal,
          //   child: Row(
          //     children: [
          //       const XBox(16),
          //       ...List.generate(
          //         6,
          //         (i) {
          //           return HomeTabOutline(
          //             text: [
          //               "Alcohol content",
          //               "Flavour",
          //               "Milkshake",
          //               "Occasion",
          //               "Dietary",
          //               "Origin",
          //             ][i],
          //             isSelected: selectedIndex == i,
          //             onTap: () {
          //               selectedIndex = i;
          //               setState(() {});
          //             },
          //           );
          //         },
          //       ),
          //       const XBox(16),
          //     ],
          //   ),
          // ),
          Builder(builder: (ctx) {
            if (productRef.isBusy) {
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(children: [
                  const XBox(16),
                  ...List.generate(
                    10,
                    (i) {
                      return Padding(
                        padding: EdgeInsets.only(
                          right: Sizer.width(16),
                        ),
                        child: Skeletonizer(
                          enabled: true,
                          child: Bone(
                            height: Sizer.height(18),
                            width: Sizer.width(70),
                            borderRadius: BorderRadius.circular(
                              Sizer.radius(8),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ]),
              );
            }
            return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(
                  _selectedCategories.length,
                  (i) {
                    final c = _selectedCategories[i];
                    return HomeTabOutline(
                      text: c,
                      isSelected: _categoryIndex == i,
                      onTap: () {
                        _categoryIndex = i;
                        setState(() {});
                      },
                    );
                  },
                ));
          }),
          const YBox(24),
          LoadableContentBuilder(
              isBusy: productRef.busy(getProductsState),
              items: productRef.products,
              loadingBuilder: (context) {
                return SizedBox(
                  height: Sizer.height(300),
                  child: ListView.separated(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                    ),
                    itemCount: 5,
                    separatorBuilder: (context, index) => const XBox(16),
                    itemBuilder: (context, index) {
                      return Skeletonizer(
                        enabled: true,
                        child: HomeProductCard(
                          onTap: () {
                            Navigator.pushNamed(
                                context, RoutePath.productDetailsScreen);
                          },
                          productModel: ProductModel(
                            name: "Glenfiddich 18yrs",
                            volume: "75cl",
                            unitPrice: 379500,
                            category: "BEST SELLER",
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
              emptyBuilder: (context) {
                return Center(
                  child: Text(
                    "No products found",
                    style: AppTypography.text18.copyWith(
                        fontWeight: FontWeight.w500, color: AppColors.black70),
                  ),
                );
              },
              contentBuilder: (context) {
                return SizedBox(
                  height: Sizer.height(300),
                  // color: AppColors.red,
                  child: ListView.separated(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                    ),
                    itemCount: productRef.products.length,
                    separatorBuilder: (context, index) => const XBox(16),
                    itemBuilder: (context, index) {
                      final p = productRef.products[index];
                      return HomeProductCard(
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.productDetailsScreen);
                        },
                        productModel: p,
                      );
                    },
                  ),
                );
              }),
          const YBox(40),
        ],
      ),
    );
  }
}
