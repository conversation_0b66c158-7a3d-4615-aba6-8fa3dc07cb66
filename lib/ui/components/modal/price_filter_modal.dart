import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class PriceFilterModal extends StatefulWidget {
  const PriceFilterModal({
    super.key,
    required this.onConfirm,
    this.isBusy = false,
  });

  final VoidCallback onConfirm;
  final bool isBusy;

  @override
  State<PriceFilterModal> createState() => _PriceFilterModalState();
}

class _PriceFilterModalState extends State<PriceFilterModal> {
  // Size options
  final List<String> _sizeOptions = ['50ml', '75ml', '100ml', '120ml'];
  String? _selectedSize;

  // Price range
  RangeValues _priceRange = const RangeValues(0, 100000);
  final double _minPrice = 0;
  final double _maxPrice = 100000;

  // Text controllers for price inputs
  final TextEditingController _fromController = TextEditingController();
  final TextEditingController _toController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fromController.text = _priceRange.start.round().toString();
    _toController.text = _priceRange.end.round().toString();
  }

  @override
  void dispose() {
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }

  void _updatePriceRange(RangeValues newRange) {
    setState(() {
      _priceRange = newRange;
      _fromController.text = newRange.start.round().toString();
      _toController.text = newRange.end.round().toString();
    });
  }

  void _updateFromText(String value) {
    final double? newValue = double.tryParse(value);
    if (newValue != null &&
        newValue >= _minPrice &&
        newValue <= _priceRange.end) {
      setState(() {
        _priceRange = RangeValues(newValue, _priceRange.end);
      });
    }
  }

  void _updateToText(String value) {
    final double? newValue = double.tryParse(value);
    if (newValue != null &&
        newValue <= _maxPrice &&
        newValue >= _priceRange.start) {
      setState(() {
        _priceRange = RangeValues(_priceRange.start, newValue);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(24),

          // Size section
          Text(
            'Size',
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlack,
            ),
          ),
          const YBox(16),
          Row(
            children: _sizeOptions.map((size) {
              final isSelected = _selectedSize == size;
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.only(
                    right: size != _sizeOptions.last ? Sizer.width(12) : 0,
                  ),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedSize = isSelected ? null : size;
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: Sizer.height(12),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected
                              ? AppColors.primaryBlack
                              : AppColors.blackBD,
                          width: isSelected ? 2 : 1,
                        ),
                        color: isSelected
                            ? AppColors.primaryBlack.withValues(alpha: 0.05)
                            : AppColors.white,
                      ),
                      child: Center(
                        child: Text(
                          size,
                          style: AppTypography.text14.copyWith(
                            color: isSelected
                                ? AppColors.primaryBlack
                                : AppColors.black70,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          const YBox(32),

          // Price range section
          Text(
            'Price range',
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlack,
            ),
          ),
          const YBox(16),

          // Range slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppColors.primaryBlack,
              inactiveTrackColor: AppColors.grayE6,
              thumbColor: AppColors.primaryBlack,
              overlayColor: AppColors.primaryBlack.withValues(alpha: 0.1),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
              trackHeight: 2,
            ),
            child: RangeSlider(
              values: _priceRange,
              min: _minPrice,
              max: _maxPrice,
              onChanged: _updatePriceRange,
            ),
          ),

          const YBox(16),

          // From and To input fields
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'From',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                      ),
                    ),
                    const YBox(8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(12),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blackBD),
                        color: AppColors.grayF6,
                      ),
                      child: TextField(
                        controller: _fromController,
                        keyboardType: TextInputType.number,
                        onChanged: _updateFromText,
                        style: AppTypography.text14,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const XBox(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'To',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                      ),
                    ),
                    const YBox(8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(12),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blackBD),
                        color: AppColors.grayF6,
                      ),
                      child: TextField(
                        controller: _toController,
                        keyboardType: TextInputType.number,
                        onChanged: _updateToText,
                        style: AppTypography.text14,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const YBox(32),

          // Apply button
          CustomBtn.solid(
            onTap: () {
              widget.onConfirm();
              Navigator.pop(context);
            },
            text: 'Apply Filter',
            isLoading: widget.isBusy,
          ),

          const YBox(40),
        ],
      ),
    );
  }
}
