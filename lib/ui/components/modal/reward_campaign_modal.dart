import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class RewardCampaignModal extends StatefulWidget {
  const RewardCampaignModal({
    super.key,
  });

  @override
  State<RewardCampaignModal> createState() => _RewardCampaignModalState();
}

class _RewardCampaignModalState extends State<RewardCampaignModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(16),
        horizontal: Sizer.width(20),
      ),
      decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(Sizer.radius(8)),
            topRight: Radius.circular(Sizer.radius(8)),
          )),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            AppImages.reward,
            height: Sizer.height(170),
            width: Sizer.screenWidth,
            fit: BoxFit.cover,
          ),
          const YBox(24),
          Text(
            "Join the Summer Rewards Campaign!",
            textAlign: TextAlign.center,
            style: AppTypography.text18.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(8),
          Text(
            "Earn tickets & instant rewards when you shop \nthis season.",
            textAlign: TextAlign.center,
            style: AppTypography.text14,
          ),
          const YBox(32),
          CustomBtn.solid(
            width: Sizer.width(220),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(
                context,
                RoutePath.customWebViewScreen,
                arguments: WebViewArg(
                  webURL: "https://bottleking.ng/rewards",
                ),
              );
            },
            online: true,
            text: "Check available offers",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
