import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ScheduleOrderModal extends ConsumerStatefulWidget {
  const ScheduleOrderModal({super.key});

  @override
  ConsumerState<ScheduleOrderModal> createState() => _ScheduleOrderModalState();
}

class _ScheduleOrderModalState extends ConsumerState<ScheduleOrderModal>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _animation;

  int selectedTabIndex = 0;
  DeliveryType _deliveryType = DeliveryType.now;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    ref.read(addressVm).isPickup
        ? _tabController.animateTo(1)
        : _tabController.animateTo(0);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        selectedTabIndex = _tabController.index;
        if (selectedTabIndex == 0) {
          ref.read(addressVm).setOrderDeliveryType(OrderDeliveryType.delivery);
        } else {
          ref.read(addressVm).setOrderDeliveryType(OrderDeliveryType.pickup);
        }
      }
    });

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Set initial animation value based on selected tab and delivery type
    if (selectedTabIndex == 1) {
      // For pickup tab, always show content
      _animationController.value = 1.0;
    } else {
      // For delivery tab, depend on delivery type
      _animationController.value =
          _deliveryType == DeliveryType.now ? 0.0 : 1.0;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        printty("isSchedule: ${ref.read(scheduleVmodel).isSchedule}");
        ref.read(scheduleVmodel).isSchedule
            ? _deliveryType = DeliveryType.schedule
            : _deliveryType = DeliveryType.now;

        setState(() {});
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            children: [
              Text(
                "Schedule order",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(16),
          Container(
            height: Sizer.height(48),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.greyF7,
              borderRadius: BorderRadius.circular(Sizer.radius(8)),
              border: Border.all(width: 1, color: AppColors.grayF0),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(6),
              vertical: Sizer.height(4),
            ),
            child: TabBar(
              splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
              physics: const NeverScrollableScrollPhysics(),
              onTap: (int value) {
                setState(() {
                  selectedTabIndex = value;
                  // Update the order delivery type based on tab selection
                  // _orderDeliveryType = value == 0
                  //     ? OrderDeliveryType.delivery
                  //     : OrderDeliveryType.pickup;

                  // // If switching to pickup tab, immediately show content
                  // if (value == 1) {
                  //   _animationController.value = 1.0;
                  // } else if (value == 0 && _deliveryType == DeliveryType.now) {
                  //   _animationController.value = 0.0;
                  // }
                });
              },
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
                color: AppColors.white,
                boxShadow: const [
                  BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: AppColors.primaryBlack,
              automaticIndicatorColorAdjustment: true,
              labelStyle: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w500,
              ),
              controller: _tabController,
              tabs: const [
                Tab(text: 'Delivery'),
                Tab(text: 'Pick-up'),
              ],
            ),
          ),
          const YBox(24),
          // Delivery options section
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScheduleCheckBoxTile(
                title: "Deliver now",
                isSelected: _deliveryType == DeliveryType.now,
                onTap: () {
                  setState(() {
                    _deliveryType = DeliveryType.now;
                    // Only reverse animation when on Delivery tab
                    // if (selectedTabIndex == 0) {
                    //   _animationController.reverse();
                    // }
                  });
                },
              ),
              const YBox(16),
              ScheduleCheckBoxTile(
                title: "Schedule order for later",
                isSelected: _deliveryType == DeliveryType.schedule,
                onTap: () {
                  setState(() {
                    _deliveryType = DeliveryType.schedule;
                    // Only reverse animation when on Delivery tab
                    // if (selectedTabIndex == 0) {
                    //   _animationController.reverse();
                    // }
                  });
                },
              ),
            ],
          ),

          // Content section - simplified without animation for pickup
          AnimatedBuilder(
            animation: _tabController.animation!,
            builder: (context, child) {
              if (_deliveryType == DeliveryType.now) {
                // When on Delivery tab with "Deliver now" selected
                _animationController.reverse();
              } else if (_deliveryType == DeliveryType.schedule) {
                // When on Delivery tab with "Schedule order for later" selected
                _animationController.forward();
              }

              return AnimatedSwitcher(
                duration: const Duration(milliseconds: 500),
                child: ScheduleLaterWidget(
                  key: ValueKey('schedule_$selectedTabIndex'),
                  sizeFactor: _animation,
                  isPickupMode: selectedTabIndex == 1,
                ),
              );
            },
          ),
          if (_deliveryType == DeliveryType.now)
            Column(
              children: [
                const YBox(30),
                CustomBtn.solid(
                  text: "Confirm",
                  onTap: () {
                    ref
                        .read(scheduleVmodel)
                        .setScheduleDeliveryType(DeliveryType.now);
                    Navigator.pop(context);
                  },
                ),
                const YBox(40),
              ],
            )
        ],
      ),
    );
  }
}

class ScheduleCheckBoxTile extends StatelessWidget {
  const ScheduleCheckBoxTile({
    super.key,
    required this.title,
    this.isSelected = false,
    this.onTap,
  });

  final String title;
  final bool isSelected;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(12),
        horizontal: Sizer.width(16),
      ),
      decoration: BoxDecoration(
        color: isSelected
            ? const Color.fromRGBO(0, 0, 0, 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(Sizer.radius(8)),
        border: Border.all(
          color: isSelected
              ? const Color.fromRGBO(0, 0, 0, 0.3)
              : AppColors.grayE6,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Text(
              title,
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
                color: isSelected ? AppColors.black : AppColors.black70,
              ),
            ),
            const Spacer(),
            CustomRadioBtn(
              isSelected: isSelected,
            )
          ],
        ),
      ),
    );
  }
}
