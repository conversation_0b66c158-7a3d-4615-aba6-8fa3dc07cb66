import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ContinueLoginModal extends StatefulWidget {
  const ContinueLoginModal({
    super.key,
  });

  @override
  State<ContinueLoginModal> createState() => _ContinueLoginModalState();
}

class _ContinueLoginModalState extends State<ContinueLoginModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(16),
          Text(
            "Log in to continue",
            textAlign: TextAlign.center,
            style: AppTypography.text24.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(8),
          Text(
            "You need to log in to your account to \ncomplete this action",
            textAlign: TextAlign.center,
            style: AppTypography.text14,
          ),
          const YBox(32),
          CustomBtn.solid(
            onTap: () {
              Navigator.pushNamed(context, RoutePath.welcomeScreen);
            },
            online: true,
            text: "Login",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
