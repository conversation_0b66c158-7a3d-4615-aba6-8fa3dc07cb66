import 'package:bottle_king_mobile/core/core.dart';

class NotifyMeModal extends StatefulWidget {
  const NotifyMeModal({super.key});

  @override
  State<NotifyMeModal> createState() => _NotifyMeModalState();
}

class _NotifyMeModalState extends State<NotifyMeModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // const YBox(16),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.end,
          //   children: [
          //     InkWell(
          //       onTap: () => Navigator.pop(context),
          //       child: SvgPicture.asset(AppSvgs.close),
          //     ),
          //   ],
          // ),
          const YBox(20),
          SvgPicture.asset(
            AppSvgs.bellRinging,
            height: Sizer.height(48),
          ),
          const YBox(16),
          Text(
            "You’re on the list!",
            textAlign: TextAlign.center,
            style: AppTypography.text18.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(12),
          Text(
            "We’ll send you a notification as soon as this \ndrink is back in stock.",
            textAlign: TextAlign.center,
            style: AppTypography.text14,
          ),
          const YBox(60),
        ],
      ),
    );
  }
}
