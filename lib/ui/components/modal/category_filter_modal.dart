import 'package:bottle_king_mobile/core/core.dart';

class CategoryFilterModal extends ConsumerStatefulWidget {
  const CategoryFilterModal({
    super.key,
    required this.onConfirm,
    this.isBusy = false,
  });

  final VoidCallback onConfirm;
  final bool isBusy;

  @override
  ConsumerState<CategoryFilterModal> createState() =>
      _CategoryFilterModalState();
}

class _CategoryFilterModalState extends ConsumerState<CategoryFilterModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(16),
          Wrap(
            runSpacing: Sizer.height(10),
            spacing: Sizer.width(10),
            children: List.generate(
              productRef.productCategories.length,
              (i) {
                final c = productRef.productCategories[i];
                return CatFilterTile(
                  title: productCategoryHelper(c.category ?? '').categoryNmae,
                  imagePath:
                      productCategoryHelper(c.category ?? '').categoryimage ??
                          AppImages.extra,
                  onTap: () {
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
          const YBox(40),
        ],
      ),
    );
  }
}

class CatFilterTile extends StatelessWidget {
  const CatFilterTile({
    super.key,
    required this.title,
    this.imagePath,
    this.onTap,
  });

  final String title;
  final String? imagePath;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(Sizer.radius(50)),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(Sizer.radius(50)),
              ),
              child: Image.asset(
                imagePath ?? AppImages.extra,
                height: Sizer.height(26),
                width: Sizer.width(26),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const XBox(10),
          Text(
            title,
            style: AppTypography.text14,
          ),
        ],
      ),
    );
  }
}
