// ignore_for_file: use_build_context_synchronously

import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class HomeProductCard extends ConsumerStatefulWidget {
  const HomeProductCard({
    super.key,
    this.onTap,
    this.fromCart = false,
    required this.productModel,
  });

  final ProductModel productModel;
  final bool fromCart;
  final Function()? onTap;

  @override
  ConsumerState<HomeProductCard> createState() => _HomeProductCardState();
}

class _HomeProductCardState extends ConsumerState<HomeProductCard> {
  bool isFavorite = false;

  bool get hasDisCount => (widget.productModel.discountPercentage != null &&
      (widget.productModel.discounts?.unit?.newPrice ?? 0) > 0);
  bool get isOutOfStock => (widget.productModel.stockQuantity ?? 0) <= 0;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: Container(
        color: AppColors.white,
        height: Sizer.height(220),
        width: Sizer.height(170),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                Container(
                  padding: EdgeInsets.all(Sizer.radius(26)),
                  height: Sizer.height(165),
                  width: Sizer.screenWidth,
                  decoration: const BoxDecoration(
                    color: AppColors.greyF7,
                  ),
                  child: MyCachedNetworkImage(
                    imageUrl: widget.productModel.image ?? "",
                  ),
                ),
                Positioned(
                  bottom: 8,
                  left: 8,
                  child: Container(
                    color: AppColors.white,
                    padding: EdgeInsets.all(Sizer.radius(2)),
                    child: LikeButton(
                      isFavorite: isFavorite,
                      iconColor: AppColors.primaryBlack,
                      splashColor: AppColors.primaryBlack,
                      onChanged: (value) async {
                        isFavorite = value;
                        setState(() {});
                        final cRef = ref.read(cartVm);
                        final res = await cRef.addToCartOrWishlist(
                          productId: widget.productModel.parentId ?? "",
                          variationId: widget.productModel.id ?? '',
                          isWishList: true,
                        );
                        handleApiResponse(
                          response: res,
                          showSuccessMessage: false,
                          onError: () {
                            isFavorite = false;
                            setState(() {});
                          },
                        );
                      },
                    ),
                  ),
                ),
                if (hasDisCount || isOutOfStock)
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (isOutOfStock) const OutOfStockWidget(),
                        if (hasDisCount)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.radius(8),
                              vertical: Sizer.height(4),
                            ),
                            color: AppColors.red00,
                            child: Text(
                              "${widget.productModel.discountPercentage ?? 0}% OFF",
                              style: AppTypography.text12.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),
            const YBox(8),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productCategoryHelper(widget.productModel.category ?? "")
                        .categoryNmae
                        .toUpperCase(),
                    style: AppTypography.text10.copyWith(
                      color: AppColors.yellow37,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const YBox(2),
                  Text(
                    widget.productModel.name ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(2),
                  Text(
                    widget.productModel.volume ?? "",
                    style: AppTypography.text12.copyWith(
                      color: AppColors.black70,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(2),
                  Row(
                    children: [
                      hasDisCount
                          ? Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.productModel.discounts?.unit?.newPrice ?? 0)}",
                                  style: AppTypography.text16.copyWith(
                                    color: AppColors.red35,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  "${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.productModel.unitPrice ?? 0)}",
                                  style: AppTypography.text12.copyWith(
                                    color: AppColors.primaryBlack,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                              ],
                            )
                          : Text(
                              "${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.productModel.unitPrice ?? 0)}",
                              style: AppTypography.text16.copyWith(
                                color: AppColors.primaryBlack,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                      const Spacer(),
                      InkWell(
                        onTap: () async {
                          final authRef = ref.read(authVm);
                          if (authRef.user == null) {
                            ModalWrapper.bottomSheet(
                                context: context,
                                widget: const ContinueLoginModal());
                            return;
                          }
                          if (isOutOfStock) {
                            final res = await ref
                                .read(productVm)
                                .productNotifyMe(
                                  authEmail: authRef.user?.email ?? "",
                                  sku: widget.productModel.sku ?? "",
                                  productId: widget.productModel.parentId ?? "",
                                  productName: widget.productModel.name ?? "",
                                  busyStateKey: widget.productModel.id,
                                );

                            handleApiResponse(
                              response: res,
                              onSuccess: () {
                                ModalWrapper.bottomSheet(
                                  context: context,
                                  widget: const NotifyMeModal(),
                                );
                              },
                            );

                            return;
                          }
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: CartPopupModal(
                              productModel: widget.productModel,
                              fromCart: widget.fromCart,
                            ),
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: (Sizer.width(16)),
                            vertical: (Sizer.radius(8)),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: AppColors.blackBD,
                            ),
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(50)),
                          ),
                          child: ref
                                  .watch(productVm)
                                  .busy(widget.productModel.id)
                              ? const LoaderIcon(
                                  size: 20,
                                  color: AppColors.gray500,
                                )
                              : SvgPicture.asset(
                                  isOutOfStock ? AppSvgs.bell2 : AppSvgs.cart,
                                  height: Sizer.height(24),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
