import 'package:bottle_king_mobile/core/core.dart';

class ShopCategoryCard extends StatelessWidget {
  const ShopCategoryCard({
    super.key,
    required this.image,
    required this.title,
    this.onTap,
  });

  final String image;
  final String title;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        children: [
          SizedBox(
            height: Sizer.height(123),
            width: double.infinity,
            child: Image.asset(
              productCategoryHelper(title).categoryimage ?? "",
              fit: BoxFit.contain,
            ),
            // child: image.isEmpty
            //     ? Image.asset(
            //         productCategoryHelper(title).categoryimage ??
            //             AppImages.extra,
            //         fit: BoxFit.contain,
            //       )
            //     : MyCachedNetworkImage(
            //         imageUrl: image,
            //         fit: BoxFit.contain,
            //       ),
          ),
          SizedBox(
            height: Sizer.height(123),
            width: double.infinity,
            // decoration: BoxDecoration(
            //   gradient: LinearGradient(
            //     begin: Alignment.topCenter,
            //     end: Alignment.bottomCenter,
            //     colors: [
            //       Colors.black.withValues(alpha: 0.5),
            //       Colors.black.withValues(alpha: 0.1),
            //     ],
            //   ),
            // ),
          ),
          Positioned(
            bottom: 10,
            left: 10,
            child: Text(
              productCategoryHelper(title).categoryNmae,
              style: AppTypography.text16.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
