import 'package:bottle_king_mobile/core/core.dart';

class ShopFilter extends StatelessWidget {
  const ShopFilter({
    super.key,
    required this.text,
    this.showArrow = true,
    this.textStyle,
    this.onTap,
  });

  final String text;
  final bool showArrow;
  final TextStyle? textStyle;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(Sizer.radius(10)),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.blackBD,
          ),
        ),
        child: Row(
          children: [
            Text(
              text,
              style: textStyle ?? AppTypography.text12,
            ),
            if (showArrow) const XBox(4),
            if (showArrow)
              Icon(
                Icons.keyboard_arrow_down,
                size: Sizer.height(20),
                color: AppColors.black70,
              )
          ],
        ),
      ),
    );
  }
}
