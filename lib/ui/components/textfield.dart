import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/core.dart';

class CustomTextField extends StatelessWidget {
  final String? hintText;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final TextEditingController? controller;
  final VoidCallback? onPressed;
  // final FormFieldValidator<String>? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLength;
  final bool? enabled;

  const CustomTextField({
    super.key,
    this.hintText,
    this.hintStyle,
    this.textStyle,
    this.controller,
    this.onPressed,
    // this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLength,
    this.enabled,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      style: textStyle,
      obscureText: obscureText,
      maxLength: maxLength,
      enabled: enabled,
      keyboardType: keyboardType,
      // validator: validator,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: AppTypography.text14.copyWith(
          color: AppColors.grey,
        ),
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.r),
          borderSide: BorderSide.none,
        ),
      ),
    );
  }
}
