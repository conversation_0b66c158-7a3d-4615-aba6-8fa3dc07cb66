import 'package:bottle_king_mobile/core/core.dart';
import 'package:pinput/pinput.dart';

class PinInputTheme {
  static defaultPinTheme({double? borderRadius, Color? bgColor}) {
    return PinTheme(
      width: Sizer.width(90),
      height: Sizer.width(64),
      margin: EdgeInsets.only(right: Sizer.width(8)),
      textStyle: TextStyle(
          fontSize: Sizer.text(32),
          color: AppColors.black70,
          fontWeight: FontWeight.w500),
      decoration: BoxDecoration(
        color: bgColor ?? AppColors.white,
        border: Border.all(
          color: AppColors.gray500,
          width: 0,
        ),
        borderRadius: BorderRadius.circular(
          borderRadius ?? Sizer.radius(8),
        ),
      ),
    );
  }

  static errorPinTheme() {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: 1, color: AppColors.red),
    );
  }

  static followPinTheme({double? borderRadius}) {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: Sizer.width(1), color: AppColors.blackBD),
      borderRadius: BorderRadius.circular(borderRadius ?? Sizer.radius(8)),
      color: AppColors.white,
    );
  }

  static focusFillPinTheme() {
    return defaultPinTheme().copyDecorationWith(
        border: Border.all(width: 0, color: AppColors.gray500),
        color: AppColors.grey.withOpacity(0.1));
  }

  static changePinTheme() {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: 0, color: AppColors.grey),
      color: AppColors.grey,
    );
  }
}
