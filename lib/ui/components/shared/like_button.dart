import 'dart:math' as math;

import 'package:bottle_king_mobile/core/core.dart';

class LikeButton extends StatefulWidget {
  const LikeButton({
    super.key,
    required this.isFavorite,
    this.iconSize,
    this.iconColor,
    this.splashColor,
    this.onChanged,
  });

  final bool isFavorite;
  final double? iconSize;
  final Color? iconColor;
  final Color? splashColor;
  final ValueChanged<bool>? onChanged;

  @override
  State<LikeButton> createState() => _LikeButtonState();
}

class _LikeButtonState extends State<LikeButton> with TickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    duration: const Duration(seconds: 1),
    vsync: this,
  );

  late final AnimationController _splashController = AnimationController(
    duration: const Duration(seconds: 1),
    vsync: this,
  );

  late final Animation<double> _splashAnimation = CurvedAnimation(
    parent: _splashController,
    curve: Curves.easeOut,
  );

  late final Animation<double> _scaleAnimation = TweenSequence([
    TweenSequenceItem(
      tween: Tween<double>(begin: 1.0, end: 0.8),
      weight: 1,
    ),
    TweenSequenceItem(
      tween: Tween<double>(begin: 0.8, end: 1.2),
      weight: 1,
    ),
    TweenSequenceItem(
      tween: Tween<double>(begin: 1.2, end: 1.0),
      weight: 1,
    ),
  ]).animate(CurvedAnimation(
    parent: _controller,
    curve: Curves.easeInOut,
  ));

  @override
  void dispose() {
    _controller.dispose();
    _splashController.dispose();
    super.dispose();
  }

  void _toggleLike() {
    setState(() {
      if (widget.onChanged != null) {
        widget.onChanged!(!widget.isFavorite);
      }
      if (!widget.isFavorite) {
        _controller.forward(from: 0);
        _splashController.forward(from: 0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleLike,
      child: SizedBox(
        width: 30,
        height: 30,
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (widget.isFavorite)
              AnimatedBuilder(
                animation: _splashAnimation,
                builder: (context, child) {
                  return CustomPaint(
                    size: const Size(30, 30),
                    painter: ParticlePainter(
                      progress: _splashAnimation.value,
                      color: widget.splashColor ?? Colors.red.withOpacity(0.5),
                    ),
                  );
                },
              ),
            ScaleTransition(
              scale: _scaleAnimation,
              child: widget.isFavorite
                  ? Icon(
                      Iconsax.heart5,
                      color: widget.iconColor ?? AppColors.red,
                    )
                  : Icon(
                      Iconsax.heart,
                      color: widget.iconColor ?? AppColors.red,
                    ),

              // Icon(
              //   widget.isFavorite ? Icons.favorite : Icons.favorite_border,
              //   size: 30,
              //   color: widget.isFavorite ? Colors.red : null,
              // ),
            ),
          ],
        ),
      ),
    );
  }
}

class ParticlePainter extends CustomPainter {
  final double progress;
  final Color color;

  ParticlePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;

    // Draw particles
    final particlePaint = Paint()
      ..color = color.withOpacity(1 - progress) // Fade out as they move
      ..style = PaintingStyle.fill;

    // More particles for a fuller effect
    for (var i = 0; i < 12; i++) {
      final angle = (i * math.pi / 6); // 12 particles evenly spaced
      final particleRadius = 4.0 * (1 - progress);
      final distance = maxRadius * progress * 1.2; // Particles move outward
      final x = center.dx + math.cos(angle) * distance;
      final y = center.dy + math.sin(angle) * distance;
      canvas.drawCircle(Offset(x, y), particleRadius, particlePaint);
    }
  }

  @override
  bool shouldRepaint(ParticlePainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
