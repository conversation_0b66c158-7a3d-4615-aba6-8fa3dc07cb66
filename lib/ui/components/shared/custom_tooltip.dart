import 'package:flutter/material.dart';

class CustomTooltip extends StatefulWidget {
  final Widget child;
  final String message;
  final Duration showDuration;
  final Duration waitDuration;
  final BoxDecoration? decoration;
  final TextStyle? textStyle;
  final EdgeInsets? padding;
  final TooltipPosition position;
  final double? maxWidth; // NEW: allow width customization
  final Color? backgroundColor; // NEW: allow background color customization

  const CustomTooltip({
    super.key,
    required this.child,
    required this.message,
    this.showDuration = const Duration(seconds: 3),
    this.waitDuration = const Duration(milliseconds: 400),
    this.decoration,
    this.textStyle,
    this.padding,
    this.position = TooltipPosition.topLeft,
    this.maxWidth,
    this.backgroundColor,
  });

  @override
  State<CustomTooltip> createState() => _CustomTooltipState();
}

class _CustomTooltipState extends State<CustomTooltip> {
  OverlayEntry? _overlayEntry;
  bool _isVisible = false;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    if (_isVisible) return;

    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => _TooltipOverlay(
        targetOffset: offset,
        targetSize: size,
        message: widget.message,
        decoration: widget.decoration,
        textStyle: widget.textStyle,
        padding: widget.padding,
        position: widget.position,
        maxWidth: widget.maxWidth,
        backgroundColor: widget.backgroundColor,
      ),
    );

    overlay.insert(_overlayEntry!);
    _isVisible = true;

    // Auto hide after duration
    Future.delayed(widget.showDuration, () {
      _removeTooltip();
    });
  }

  void _removeTooltip() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isVisible = false;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _showTooltip();
      },
      child: widget.child,
    );
  }
}

class _TooltipOverlay extends StatefulWidget {
  final Offset targetOffset;
  final Size targetSize;
  final String message;
  final BoxDecoration? decoration;
  final TextStyle? textStyle;
  final EdgeInsets? padding;
  final TooltipPosition position;
  final double? maxWidth;
  final Color? backgroundColor;

  const _TooltipOverlay({
    required this.targetOffset,
    required this.targetSize,
    required this.message,
    this.decoration,
    this.textStyle,
    this.padding,
    required this.position,
    this.maxWidth,
    this.backgroundColor,
  });

  @override
  State<_TooltipOverlay> createState() => _TooltipOverlayState();
}

class _TooltipOverlayState extends State<_TooltipOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Offset _calculatePosition(Size tooltipSize) {
    const spacing = 8.0;
    double left = 0;
    double top = 0;

    switch (widget.position) {
      case TooltipPosition.topLeft:
        left = widget.targetOffset.dx +
            widget.targetSize.width -
            tooltipSize.width;
        top = widget.targetOffset.dy - tooltipSize.height - spacing;
        break;
      case TooltipPosition.topRight:
        left = widget.targetOffset.dx;
        top = widget.targetOffset.dy - tooltipSize.height - spacing;
        break;
      case TooltipPosition.topCenter:
        left = widget.targetOffset.dx +
            (widget.targetSize.width - tooltipSize.width) / 2;
        top = widget.targetOffset.dy - tooltipSize.height - spacing;
        break;
      case TooltipPosition.bottomRight:
        left = widget.targetOffset.dx +
            widget.targetSize.width -
            tooltipSize.width;
        top = widget.targetOffset.dy + widget.targetSize.height + spacing;
        break;
      case TooltipPosition.bottomLeft:
        left = widget.targetOffset.dx;
        top = widget.targetOffset.dy + widget.targetSize.height + spacing;
        break;
      case TooltipPosition.bottomCenter:
        left = widget.targetOffset.dx +
            (widget.targetSize.width - tooltipSize.width) / 2;
        top = widget.targetOffset.dy + widget.targetSize.height + spacing;
        break;
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    if (left < 8) left = 8;
    if (left + tooltipSize.width > screenWidth - 8) {
      left = screenWidth - tooltipSize.width - 8;
    }
    if (top < 8) top = 8;
    if (top + tooltipSize.height > screenHeight - 8) {
      top = screenHeight - tooltipSize.height - 8;
    }

    return Offset(left, top);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: GestureDetector(
            onTap: () {},
            child: Container(color: Colors.transparent),
          ),
        ),
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Opacity(
              opacity: _animation.value,
              child: Transform.scale(
                scale: 0.8 + (_animation.value * 0.2),
                child: child,
              ),
            );
          },
          child: _TooltipContent(
            message: widget.message,
            decoration: widget.decoration,
            textStyle: widget.textStyle,
            padding: widget.padding,
            calculatePosition: _calculatePosition,
            maxWidth: widget.maxWidth,
            backgroundColor: widget.backgroundColor,
          ),
        ),
      ],
    );
  }
}

class _TooltipContent extends StatelessWidget {
  final String message;
  final BoxDecoration? decoration;
  final TextStyle? textStyle;
  final EdgeInsets? padding;
  final Offset Function(Size) calculatePosition;
  final double? maxWidth;
  final Color? backgroundColor;

  const _TooltipContent({
    required this.message,
    this.decoration,
    this.textStyle,
    this.padding,
    required this.calculatePosition,
    this.maxWidth,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveStyle = (textStyle ??
            const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ))
        .copyWith(decoration: TextDecoration.none);

    return CustomSingleChildLayout(
      delegate: _TooltipPositionDelegate(calculatePosition),
      child: Container(
        decoration: decoration ??
            BoxDecoration(
              color: backgroundColor ?? Colors.black87,
              borderRadius: BorderRadius.circular(8),
            ),
        padding:
            padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        constraints: BoxConstraints(maxWidth: maxWidth ?? 250),
        child: Text(
          message,
          textAlign: TextAlign.center,
          style: effectiveStyle,
        ),
      ),
    );
  }
}

class _TooltipPositionDelegate extends SingleChildLayoutDelegate {
  final Offset Function(Size) calculatePosition;

  _TooltipPositionDelegate(this.calculatePosition);

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    return calculatePosition(childSize);
  }

  @override
  bool shouldRelayout(_TooltipPositionDelegate oldDelegate) => true;
}

enum TooltipPosition {
  topLeft,
  topRight,
  topCenter,
  bottomLeft,
  bottomRight,
  bottomCenter,
}
