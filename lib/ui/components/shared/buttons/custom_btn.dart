import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CustomBtn {
  static Widget solid({
    required Function()? onTap,
    bool online = true,
    bool isOutline = false,
    required String text,
    bool isLoading = false,
    BorderRadiusGeometry? borderRadius,
    double? width,
    double? height,
    Color? offlineColor,
    Color? onlineColor,
    Color? outlineColor,
    Color? textColor,
    TextStyle? textStyle,
  }) {
    return IgnorePointer(
      ignoring: !online || isLoading,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: width ?? Sizer.screenWidth,
          height: Sizer.height(height ?? 50),
          decoration: (online && !isLoading)
              ? BoxDecoration(
                  borderRadius: borderRadius,
                  color: isOutline
                      ? AppColors.transparent
                      : onlineColor ?? AppColors.black,
                  border: isOutline
                      ? Border.all(color: outlineColor ?? AppColors.black)
                      : null,
                )
              : BoxDecoration(
                  borderRadius: borderRadius,
                  color: offlineColor ?? AppColors.grayD9,
                ),
          child: Center(
            child: isLoading
                ? const LoaderIcon(size: 30)
                : Text(
                    text,
                    style: textStyle ??
                        AppTypography.text14.copyWith(
                          fontWeight: FontWeight.w600,
                          color: online
                              ? textColor ?? AppColors.white
                              : AppColors.gray500,
                        ),
                  ),
          ),
        ),
      ),
    );
  }

  static Widget withChild({
    required Function()? onTap,
    bool online = true,
    bool isOutline = false,
    BorderRadiusGeometry? borderRadius,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    Color? offlineColor,
    Color? onlineColor,
    Color? outlineColor,
    required Widget child,
  }) {
    return IgnorePointer(
      ignoring: !online,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: width ?? Sizer.screenWidth,
          height: Sizer.height(height ?? 60),
          decoration: online
              ? BoxDecoration(
                  borderRadius: borderRadius,
                  color: isOutline
                      ? AppColors.transparent
                      : onlineColor ?? AppColors.grey,
                  border: isOutline
                      ? Border.all(
                          color: outlineColor ?? AppColors.grey,
                        )
                      : null,
                )
              : BoxDecoration(
                  borderRadius: borderRadius,
                  color: offlineColor ?? AppColors.grayD9,
                ),
          child: Center(child: child),
        ),
      ),
    );
  }
}

class BtnLoadState extends StatelessWidget {
  const BtnLoadState({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Sizer.height(48),
      width: Sizer.screenWidth,
      decoration: const BoxDecoration(
        color: AppColors.grayD9,
      ),
      child: const Center(
        child: LoaderIcon(
          size: 30,
        ),
      ),
    );
  }
}
