import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class AddressPickupTab extends ConsumerWidget {
  const AddressPickupTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pick up in store',
          style: AppTypography.text14.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.black70,
          ),
        ),
        const YBox(10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.location_on_outlined,
              color: AppColors.black70,
            ),
            const XBox(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Store address',
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.black70,
                    ),
                  ),
                  const YBox(10),
                  Text(
                    AppText.pickupAddress,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const YBox(16),
        const Divider(thickness: 1, color: AppColors.grayE6),
        const YBox(20),
        CustomBtn.solid(
          onTap: () {
            ref.read(addressVm).setOrderDeliveryType(OrderDeliveryType.pickup);
            Navigator.pop(context, OrderDeliveryType.pickup);
          },
          online: true,
          text: "Confirm",
        ),
      ],
    );
  }
}
