import 'package:bottle_king_mobile/core/core.dart';

class PickupOrderWidget extends ConsumerStatefulWidget {
  const PickupOrderWidget({
    super.key,
    required this.sizeFactor,
  });

  final Animation<double> sizeFactor;

  @override
  ConsumerState<PickupOrderWidget> createState() => _PickupOrderWidgetState();
}

class _PickupOrderWidgetState extends ConsumerState<PickupOrderWidget> {
  @override
  Widget build(BuildContext context) {
    return SizeTransition(
      sizeFactor: widget.sizeFactor,
      child: FadeTransition(
        opacity: widget.sizeFactor,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.location_on_outlined,
              color: AppColors.black70,
            ),
            const XBox(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Store address',
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.black70,
                    ),
                  ),
                  const YBox(10),
                  Text(
                    "1b Rock drive, Lekki Phase one",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
