import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:flutter/cupertino.dart';

class ScheduleLaterWidget extends ConsumerStatefulWidget {
  const ScheduleLaterWidget({
    super.key,
    required this.sizeFactor,
    this.isPickupMode = false,
  });

  final Animation<double> sizeFactor;
  final bool isPickupMode;

  @override
  ConsumerState<ScheduleLaterWidget> createState() =>
      _ScheduleLaterWidgetState();
}

class _ScheduleLaterWidgetState extends ConsumerState<ScheduleLaterWidget> {
  final _timeController = TextEditingController();
  final _dateController = TextEditingController();
  DateTime? _selectedTime;
  DateTime? _selectedDate;
  String? _selectedTimeString;
  String? _selectedFormattedTimeString;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ref.read(scheduleVmodel).isSchedule) {
        _dateController.text = ref.read(scheduleVmodel).formatedScheduleDate;
        _timeController.text =
            ref.read(scheduleVmodel).formatedScheduleTime ?? "";
      }
    });
  }

  @override
  void dispose() {
    _timeController.dispose();
    super.dispose();
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour < 12 ? 'AM' : 'PM';

    // Set the schedule time string
    // ref.read(scheduleVm).setScheduleTimeString('$hour:$minute');
    _selectedTimeString = '$hour:$minute';
    _selectedFormattedTimeString = '$hour:$minute $period';
    return '$hour:$minute $period';
  }

  bool get enableConfirmButton =>
      _selectedDate != null && _selectedTimeString != null;

  @override
  Widget build(BuildContext context) {
    return SizeTransition(
      sizeFactor: widget.sizeFactor,
      child: FadeTransition(
        opacity: widget.sizeFactor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(16),
            const Divider(thickness: 1, color: AppColors.grayE6),
            const YBox(16),
            Text(
              widget.isPickupMode
                  ? 'Select a time frame for your order to be picked up'
                  : 'Select a time frame for your order to be delivered',
              style: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(12),
            CustomTextField(
              controller: _dateController,
              hintText: "Select date",
              isReadOnly: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
              suffixIcon: Icon(
                Iconsax.calendar_1,
                size: Sizer.height(24),
                color: AppColors.black70,
              ),
              onTap: () {
                CustomCupertinoDatePicker(
                  context: context,
                  minimumDate: DateTime.now(),
                  onDateTimeChanged: (dateTime) {
                    // Implementation for date selection
                    printty("Selected date: $_selectedDate");
                    _selectedDate = dateTime;
                    _dateController.text =
                        AppUtils.dayWithSuffixMonthAndYear(dateTime);
                    setState(() {});
                  },
                ).show();
              },
            ),
            const YBox(20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.isPickupMode
                      ? 'Select a time for your order to be picked up'
                      : 'Select a time for your order to be delivered',
                  style: AppTypography.text14.copyWith(
                    color: AppColors.black70,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(12),
                CustomTextField(
                  controller: _timeController,
                  hintText: "Select time",
                  isReadOnly: true,
                  suffixIcon: Padding(
                    padding: EdgeInsets.all(Sizer.radius(12)),
                    child: SvgPicture.asset(AppSvgs.clock),
                  ),
                  onTap: () {
                    CustomCupertinoDatePicker(
                      context: context,
                      mode: CupertinoDatePickerMode.time,
                      initialDateTime: _selectedTime ?? DateTime.now(),
                      onDateTimeChanged: (dateTime) {
                        printty("Selected time: $_selectedTime");
                        _selectedTime = dateTime;
                        _timeController.text = _formatTime(dateTime);
                        setState(() {});
                      },
                    ).show();
                  },
                ),
                const YBox(30),
                CustomBtn.solid(
                  text: "Confirm",
                  online: enableConfirmButton,
                  onTap: () {
                    if (enableConfirmButton) {
                      ref.read(scheduleVmodel).setScheduleDate(_selectedDate!);
                      ref.read(scheduleVmodel).setScheduleTimeString(
                            time: _selectedTimeString ?? "",
                            formattedTime: _selectedFormattedTimeString ?? "",
                          );
                      ref
                          .read(scheduleVmodel)
                          .setScheduleDeliveryType(DeliveryType.schedule);
                      Navigator.pop(context);
                    }
                  },
                ),
                const YBox(40),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
