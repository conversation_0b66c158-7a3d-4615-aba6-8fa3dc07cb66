// import 'package:flutter/gestures.dart';
// import 'package:quick_basket/core/core.dart';

// import '../shared/textfields/custom_checkbox.dart';

// class RegTermsAndConditions extends StatelessWidget {
//   const RegTermsAndConditions({
//     super.key,
//     this.isSelected = false,
//     this.onTap,
//   });

//   final bool isSelected;
//   final VoidCallback? onTap;

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         CustomCheckbox(
//           isSelected: isSelected,
//           onTap: onTap,
//         ),
//         const XBox(8),
//         Expanded(
//           child: RichText(
//               text: TextSpan(
//             children: [
//               TextSpan(
//                 text: "I Agree with ",
//                 style: AppTypography.text14.copyWith(
//                   color: AppColors.neutral100,
//                   fontWeight: FontWeight.w400,
//                   height: 1.2,
//                 ),
//               ),
//               TextSpan(
//                 text: "Terms of Service",
//                 style: AppTypography.text14.copyWith(
//                   color: AppColors.primaryYellow,
//                   fontWeight: FontWeight.w500,
//                   height: 1.2,
//                 ),
//                 recognizer: TapGestureRecognizer()..onTap = () {},
//               ),
//               TextSpan(
//                 text: " and ",
//                 style: AppTypography.text14.copyWith(
//                   color: AppColors.neutral100,
//                   fontWeight: FontWeight.w400,
//                   height: 1.2,
//                 ),
//               ),
//               TextSpan(
//                 text: "Privacy Policy",
//                 style: AppTypography.text14.copyWith(
//                   color: AppColors.primaryYellow,
//                   fontWeight: FontWeight.w500,
//                   height: 1.2,
//                 ),
//                 recognizer: TapGestureRecognizer()..onTap = () {},
//               ),
//             ],
//           )),
//         )
//       ],
//     );
//   }
// }
