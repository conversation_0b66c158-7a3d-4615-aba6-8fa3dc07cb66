import 'dart:async';

import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/dialogs/resend_otp_option_dialog.dart';

class ResendCode extends ConsumerStatefulWidget {
  const ResendCode({
    super.key,
    this.onResendCode,
  });

  final VoidCallback? onResendCode;

  @override
  ConsumerState<ResendCode> createState() => _ResendCodeState();
}

class _ResendCodeState extends ConsumerState<ResendCode> {
  int _secondsRemaining = 120;
  Timer? _timer;
  bool _isResending = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer?.cancel(); // Cancel any existing timer
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_secondsRemaining > 0) {
            _secondsRemaining--;
          } else {
            _timer?.cancel();
          }
        });
      }
    });
  }

  void _restartTimer() {
    if (mounted) {
      setState(() {
        _secondsRemaining = 120;
      });
      _startTimer();
    }
  }

  Future<void> _handleResendOTP() async {
    if (_isResending) return;

    try {
      setState(() {
        _isResending = true;
      });

      final response = await ref.read(authVm.notifier).resendOTP();

      if (mounted) {
        handleApiResponse(
          response: response,
          onSuccess: () {
            _restartTimer();
            if (widget.onResendCode != null) {
              widget.onResendCode!();
            }
          },
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  void _showResendOptions() {
    ModalWrapper.showCustomDialog(
      context,
      child: ResendOtpOptionDialog(
        onWhatsAppTap: () {
          Navigator.pop(context);
          _handleResendOTP();
        },
        onSmsTap: () {
          Navigator.pop(context);
          _handleResendOTP();
        },
        isLoading: _isResending,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isTimedOut = _secondsRemaining == 0;

    if (isTimedOut) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          InkWell(
            onTap: !_isResending ? _showResendOptions : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_isResending) ...[
                  SizedBox(
                    height: Sizer.height(14),
                    width: Sizer.height(14),
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                  const XBox(8),
                ],
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Didn’t receive code? ",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.black70,
                        ),
                      ),
                      TextSpan(
                        text: "Resend",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.primaryBlack,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )
        ],
      );
    }
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Resend code in ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              _formatTime(_secondsRemaining),
              style: AppTypography.text14.copyWith(
                color: AppColors.primaryBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    String formattedMinutes = (minutes < 10) ? '0$minutes' : '$minutes';
    String formattedSeconds =
        (remainingSeconds < 10) ? '0$remainingSeconds' : '$remainingSeconds';
    return '$formattedMinutes:$formattedSeconds';
  }

  // getDurationInMins() {
  //   seconds = (duration % 60).toInt().toString();
  //   min = (duration ~/ 60).toInt().toString();
  // }
}
