import 'package:bottle_king_mobile/core/core.dart';

class SocialBtn extends StatelessWidget {
  const SocialBtn({
    super.key,
    required this.iconPath,
    required this.btnText,
    this.onTap,
  });

  final String iconPath;
  final String btnText;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(
          Sizer.radius(12),
        ),
        decoration: BoxDecoration(
            border: Border.all(
          color: AppColors.grayDD,
        )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath),
            const XBox(12),
            Text(
              btnText,
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
