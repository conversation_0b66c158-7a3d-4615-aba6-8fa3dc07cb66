import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class FaqTabView extends ConsumerStatefulWidget {
  const FaqTabView({
    super.key,
  });

  @override
  ConsumerState<FaqTabView> createState() => _FaqTabViewState();
}

class _FaqTabViewState extends ConsumerState<FaqTabView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   "FAQ",
        //   style: AppTypography.text18.copyWith(
        //     fontWeight: FontWeight.w500,
        //   ),
        // ),
        // const YBox(16),
        // CustomTextField(
        //   hintText: 'Search  for help',
        //   fillColor: AppColors.grayF6,
        //   hideBorder: true,
        //   borderRadius: 0,
        //   suffixIcon: Padding(
        //     padding: const EdgeInsets.all(6),
        //     child: Container(
        //       width: Sizer.width(40),
        //       padding: EdgeInsets.symmetric(
        //         horizontal: Sizer.width(10),
        //         vertical: Sizer.height(10),
        //       ),
        //       color: AppColors.primaryBlack,
        //       child: SvgPicture.asset(AppSvgs.search),
        //     ),
        //   ),
        // ),
        // const YBox(16),
        Expanded(
          child: ListView.separated(
            padding: EdgeInsets.only(
              bottom: Sizer.height(100),
            ),
            itemBuilder: (ctx, i) {
              return FaqListTile(
                title: AppText.faqs[i]["title"],
                content: AppText.faqs[i]["content"],
              );
            },
            separatorBuilder: (_, __) => const YBox(16),
            itemCount: AppText.faqs.length,
          ),
        ),
      ],
    );
  }
}
