import 'package:bottle_king_mobile/core/core.dart';

class FaqListTile extends StatefulWidget {
  const FaqListTile({
    super.key,
    required this.title,
    required this.content,
    this.isInitiallyExpanded = false,
    this.onExpansionChanged,
    this.titleStyle,
    this.contentStyle,
    this.backgroundColor,
    this.borderRadius,
    this.animationDuration = const Duration(milliseconds: 300),
    this.padding,
    this.margin,
    this.iconSize,
    this.expandedIcon,
    this.collapsedIcon,
    this.iconColor,
  });

  final String title;
  final String content;
  final bool isInitiallyExpanded;
  final ValueChanged<bool>? onExpansionChanged;
  final TextStyle? titleStyle;
  final TextStyle? contentStyle;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Duration animationDuration;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? iconSize;
  final IconData? expandedIcon;
  final IconData? collapsedIcon;
  final Color? iconColor;

  @override
  State<FaqListTile> createState() => _FaqListTileState();
}

class _FaqListTileState extends State<FaqListTile>
    with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _animationController;
  late Animation<double> _iconRotationAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isInitiallyExpanded;

    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _iconRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (_isExpanded) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
    widget.onExpansionChanged?.call(_isExpanded);
  }

  Widget _buildContentText() {
    // Check if content contains bullet points with bold text
    if (widget.content.contains('•') && widget.content.contains('**')) {
      return _buildRichText();
    }

    // Fallback to regular text
    return Text(
      widget.content,
      style: widget.contentStyle ??
          AppTypography.text12.copyWith(
            fontWeight: FontWeight.w400,
            color: AppColors.primaryBlack.withOpacity(0.8),
            height: 1.5,
          ),
    );
  }

  Widget _buildRichText() {
    final baseStyle = widget.contentStyle ??
        AppTypography.text12.copyWith(
          fontWeight: FontWeight.w400,
          color: AppColors.primaryBlack.withOpacity(0.8),
          height: 1.5,
        );

    final boldStyle = baseStyle.copyWith(fontWeight: FontWeight.bold);

    List<TextSpan> spans = [];
    List<String> lines = widget.content.split('\n');

    for (int i = 0; i < lines.length; i++) {
      String line = lines[i].trim();

      if (line.isEmpty) {
        if (i < lines.length - 1) {
          spans.add(TextSpan(text: '\n', style: baseStyle));
        }
        continue;
      }

      // Handle bullet points
      if (line.startsWith('•')) {
        spans.add(TextSpan(text: '• ', style: baseStyle));
        String restOfLine = line.substring(1).trim();

        // Parse bold text within the line
        _parseBoldText(restOfLine, spans, baseStyle, boldStyle);
      } else {
        // Parse bold text for non-bullet lines
        _parseBoldText(line, spans, baseStyle, boldStyle);
      }

      // Add line break if not the last line
      if (i < lines.length - 1) {
        spans.add(TextSpan(text: '\n', style: baseStyle));
      }
    }

    return RichText(
      text: TextSpan(children: spans),
    );
  }

  void _parseBoldText(String text, List<TextSpan> spans, TextStyle baseStyle,
      TextStyle boldStyle) {
    RegExp boldRegex = RegExp(r'\*\*(.*?)\*\*');
    int lastEnd = 0;

    for (Match match in boldRegex.allMatches(text)) {
      // Add text before the bold part
      if (match.start > lastEnd) {
        spans.add(TextSpan(
          text: text.substring(lastEnd, match.start),
          style: baseStyle,
        ));
      }

      // Add the bold text
      spans.add(TextSpan(
        text: match.group(1),
        style: boldStyle,
      ));

      lastEnd = match.end;
    }

    // Add remaining text after the last bold part
    if (lastEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastEnd),
        style: baseStyle,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin ?? EdgeInsets.symmetric(vertical: Sizer.height(4)),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? AppColors.greyF7,
        borderRadius: widget.borderRadius,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _toggleExpansion,
          borderRadius: widget.borderRadius,
          child: Padding(
            padding: widget.padding ??
                EdgeInsets.symmetric(
                  vertical: Sizer.height(16),
                  horizontal: Sizer.width(16),
                ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.title,
                        style: widget.titleStyle ??
                            AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryBlack,
                            ),
                      ),
                    ),
                    SizedBox(width: Sizer.width(12)),
                    AnimatedBuilder(
                      animation: _iconRotationAnimation,
                      builder: (context, child) {
                        return RotationTransition(
                          turns: _iconRotationAnimation,
                          child: Icon(
                            _isExpanded
                                ? (widget.expandedIcon ?? Icons.remove)
                                : (widget.collapsedIcon ?? Icons.add),
                            color: widget.iconColor ?? AppColors.primaryBlack,
                            size: widget.iconSize ?? Sizer.height(24),
                          ),
                        );
                      },
                    ),
                  ],
                ),
                AnimatedSize(
                  duration: widget.animationDuration,
                  curve: Curves.easeInOut,
                  alignment: Alignment.topCenter,
                  child: _isExpanded
                      ? Padding(
                          padding: EdgeInsets.only(
                            top: Sizer.height(16),
                            right: Sizer.width(8),
                          ),
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: _buildContentText(),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}


 // Basic usage
  // FaqListTile(
  //   title: "What is Flutter?",
  //   content: "Flutter is Google's UI toolkit for building beautiful, natively compiled applications for mobile, web, desktop, and embedded devices from a single codebase.",
  // ),
  
  // // With rich text formatting (bullet points and bold text)
  // FaqListTile(
  //   title: "What are the benefits of signing up to BottleKing?",
  //   content: "Signing up for BottleKing offers several benefits, including:\n"
  //       "• **Loyalty Program**: Earn points for every ₦500 spent, redeemable for discounts on future purchases.\n"
  //       "• **Referral Discounts**: Share your referral code to receive a 15% discount when someone uses it.\n"
  //       "• **Exclusive Deals and Updates**: Access special deals, new product releases, and events.\n"
  //       "• **Fast Delivery**: Enjoy delivery within an hour in Lagos.",
  //   isInitiallyExpanded: true,
  // ),