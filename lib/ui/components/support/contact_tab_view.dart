import 'package:bottle_king_mobile/core/core.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactTabView extends StatelessWidget {
  const ContactTabView({
    super.key,
  });

  // Contact information constants
  static const String _phoneNumber = "08140099999";
  static const String _emailAddress = "<EMAIL>";
  static const String _whatsappNumber =
      "2348140099999"; // Nigerian format for WhatsApp

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const YBox(10),
        ContactUsWidget(
          title: "Email us",
          subTitle: "Replies within 8 hrs",
          iconPath: AppSvgs.sms,
          onTap: () => _launchEmail(),
        ),
        const YBox(16),
        ContactUsWidget(
          title: "Chat on WhatsApp",
          subTitle: "Replies ASAP",
          iconPath: AppSvgs.whatsapp,
          onTap: () => _launchWhatsApp(),
        ),
        const YBox(16),
        ContactUsWidget(
          title: "Call us",
          subTitle: "Business hours everyday",
          iconPath: AppSvgs.call,
          onTap: () => _launchPhone(),
        ),
      ],
    );
  }

  /// Launch phone dialer with pre-filled number
  Future<void> _launchPhone() async {
    final Uri phoneUri = Uri(
      scheme: 'tel',
      path: _phoneNumber,
    );

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        _showErrorMessage('Unable to open phone dialer');
      }
    } catch (e) {
      printty('Error launching phone: $e');
      _showErrorMessage('Unable to open phone dialer');
    }
  }

  /// Launch email app with pre-filled recipient
  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: _emailAddress,
      query: 'subject=${Uri.encodeComponent('Support Request')}',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        _showErrorMessage('Unable to open email app');
      }
    } catch (e) {
      printty('Error launching email: $e');
      _showErrorMessage('Unable to open email app');
    }
  }

  /// Launch WhatsApp with pre-filled number
  Future<void> _launchWhatsApp() async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$_whatsappNumber?text=${Uri.encodeComponent('Hello! I need assistance with my order.')}',
    );

    try {
      if (await canLaunchUrl(whatsappUri)) {
        await launchUrl(
          whatsappUri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        _showErrorMessage('WhatsApp is not installed on this device');
      }
    } catch (e) {
      printty('Error launching WhatsApp: $e');
      _showErrorMessage('Unable to open WhatsApp');
    }
  }

  /// Show error message to user
  void _showErrorMessage(String message) {
    FlushBarToast.fLSnackBar(
      snackBarType: SnackBarType.warning,
      message: message,
    );
  }
}

class ContactUsWidget extends StatelessWidget {
  const ContactUsWidget({
    super.key,
    required this.title,
    required this.subTitle,
    required this.iconPath,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String iconPath;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(12),
          horizontal: Sizer.width(16),
        ),
        decoration: BoxDecoration(
          color: AppColors.greyF7,
          borderRadius: BorderRadius.circular(Sizer.radius(10)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: .05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            SvgPicture.asset(iconPath),
            const XBox(16),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(4),
                  Text(
                    subTitle,
                    style: AppTypography.text12.copyWith(
                      color: AppColors.black70,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
