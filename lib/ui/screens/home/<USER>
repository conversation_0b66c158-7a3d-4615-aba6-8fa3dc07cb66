import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final ScrollController _scrollController = ScrollController();

  // OrderDeliveryType _orderDeliveryType = OrderDeliveryType.delivery;
  int _categoryIndex = -1;
  String? _selectedCategory;
  String? _selectedExtraCategory;

  final Map<String, String> _extraCategoryMap = {
    "Discounts": "discount",
    "Best sellers": "bestsellers",
    "Recommended": "recommended",
    "New arrivals": "newarrival",
  };

  @override
  void initState() {
    super.initState();
    final prodVm = ref.read(productVm);
    // Add listener to load more data when user reaches the end of the list
    _scrollController.addListener(() {
      if (_scrollController.position.maxScrollExtent ==
          _scrollController.offset) {
        if (prodVm.totalNumber >= prodVm.limit) {
          printty("totalNumber: ${prodVm.totalNumber}");
          WidgetsBinding.instance.addPostFrameCallback((_) {
            getProductsByCategory(
              category: _selectedCategory,
              extraCategory: _selectedExtraCategory,
              isFirstCall: false,
            );
          });
          printty("Paginating");
        } else {
          printty("No more data");
        }
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final addressRef = ref.read(addressVm);
      final authRef = ref.read(authVm);
      // check if no address
      if (authRef.user != null && addressRef.address.isEmpty) {
        // ignore: use_build_context_synchronously
        Navigator.pushNamed(context, RoutePath.noAddressScreen);
      }
      _initSetup();
    });
  }

  _initSetup() async {
    final prodRef = ref.read(productVm);
    final authRef = ref.read(authVm);

    ref.read(appConfigVmodel).initialize();

    // Get csat order id
    _getCsatOrderId();

    prodRef.getProductCategories();

    getProductsByCategory(extraCategory: _selectedExtraCategory);
    await authRef.getUser();

    // Get device token
    if (authRef.user != null) {
      authRef.updateToken(
          await StorageService.getStringItem(StorageKey.deviceToken) ?? "");
    }

    // Check for rating Eligibility
    // final orderRef = ref.read(orderViewModel);
    // final r = await orderRef.checkCsatEligibility(
    //   params: RateFeedbackParams(
    //     orderId: "64e000100000000000000000",
    //   ),
    // );

    // handleApiResponse(
    //   showErrorMessage: false,
    //   showSuccessMessage: false,
    //   response: r,
    //   onSuccess: () {
    //     if (orderRef.rateStatusModel?.ces?.submitted == true) {
    //       final index = (orderRef.rateStatusModel?.ces?.rating ?? 0) - 1;
    //       selectedIndex = index;
    //       resRateIndex = index;
    //     }

    //     setState(() {});
    //   },
    // );
  }

  getProductsByCategory({
    String? category,
    String? extraCategory,
    bool? isFirstCall,
  }) async {
    ref.read(productVm).getProductsByCategoryFilter(
          isFirstCall: isFirstCall ?? true,
          category: category,
          isBestSeller: extraCategory == "bestsellers",
          isRecommended: extraCategory == "recommended",
          isNewArrival: extraCategory == "newarrival",
          hasDiscount: extraCategory == "discount",
        );
  }

  _getCsatOrderId() async {
    final orderId = await StorageService.getStringItem(StorageKey.csatKey);

    if (orderId != null && orderId.isNotEmpty) {
      if (mounted) {
        Navigator.pushNamed(
          context,
          RoutePath.csatScreen,
          arguments: CsatArg(action: StorageKey.csatKey, orderId: orderId),
        );
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final authRef = ref.watch(authVm);
    final productRef = ref.watch(productVm);
    final addressRef = ref.watch(addressVm);
    final configRef = ref.watch(appConfigVmodel);

    printty("HomeScreen build Vm ${configRef.showPromo}");
    return CartFloatingActionButton(
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(140)),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: Sizer.height(10),
            ),
            color: AppColors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HomeTab(
                        text: "Delivery",
                        isSelected: addressRef.orderDeliveryType ==
                            OrderDeliveryType.delivery,
                        margin: EdgeInsets.only(
                          right: Sizer.width(16),
                        ),
                        onTap: () {
                          ref
                              .read(addressVm.notifier)
                              .setOrderDeliveryType(OrderDeliveryType.delivery);
                        },
                      ),
                      HomeTab(
                        text: "Pick-up",
                        isSelected: addressRef.orderDeliveryType ==
                            OrderDeliveryType.pickup,
                        margin: EdgeInsets.only(
                          right: Sizer.width(16),
                        ),
                        onTap: () {
                          ref
                              .read(addressVm)
                              .setOrderDeliveryType(OrderDeliveryType.pickup);
                        },
                      ),

                      HomeTab(
                        text: "Schedule",
                        margin: EdgeInsets.only(
                          right: Sizer.width(16),
                        ),
                        isSelected: ref.watch(scheduleVmodel).isSchedule,
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: const ScheduleOrderModal(),
                          );
                        },
                      ),
                      // HomeTab(
                      //   text: "Group Order",
                      //   margin: EdgeInsets.only(
                      //     right: Sizer.width(16),
                      //   ),
                      //   onTap: () {
                      //     Navigator.pushNamed(context, RoutePath.groupOrderScreen);
                      //   },
                      // ),
                    ],
                  ),
                ),
                const YBox(10),
                const Divider(thickness: 4, color: AppColors.greyF7),
                const YBox(10),

                // Address
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        fit: FlexFit.loose,
                        child: InkWell(
                          onTap: () {
                            ModalWrapper.bottomSheet(
                              context: context,
                              widget: const AddressModal(),
                            );
                          },
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxWidth: !configRef.showPromo
                                  ? Sizer.screenWidth
                                  : Sizer.screenWidth * 0.7,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  fit: FlexFit.loose,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${addressRef.orderDeliveryType.title} location',
                                        style: AppTypography.text14.copyWith(
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.black70,
                                        ),
                                      ),
                                      const YBox(2),
                                      Text(
                                        addressRef.address,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTypography.text14.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                const XBox(8),
                                const Icon(
                                  Icons.keyboard_arrow_down,
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (configRef.showPromo)
                        InkWell(
                          onTap: () {
                            ModalWrapper.bottomSheet(
                              context: context,
                              widget: const RewardCampaignModal(),
                            );
                          },
                          child: SvgPicture.asset(
                            AppSvgs.promoTrigger,
                            height: Sizer.height(36),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await _initSetup();
          },
          child: ListView(
            controller: _scrollController,
            padding: EdgeInsets.only(
              bottom: Sizer.height(150),
            ),
            children: [
              const YBox(20),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
                child: CustomTextField(
                  hintText: 'Find your favourite drinks',
                  fillColor: AppColors.grayF6,
                  hideBorder: true,
                  isReadOnly: true,
                  borderRadius: 0,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.all(6),
                    child: Container(
                      width: Sizer.width(40),
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(10),
                        vertical: Sizer.height(10),
                      ),
                      color: AppColors.primaryBlack,
                      child: SvgPicture.asset(AppSvgs.search),
                    ),
                  ),
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.searchProductScreen,
                    );
                  },
                ),
              ),
              const YBox(16),
              Builder(builder: (ctx) {
                if (productRef.isBusy) {
                  return SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(children: [
                      const XBox(16),
                      ...List.generate(
                        10,
                        (i) {
                          return Padding(
                            padding: EdgeInsets.only(
                              right: Sizer.width(16),
                            ),
                            child: Skeletonizer(
                              enabled: true,
                              child: Bone(
                                height: Sizer.height(18),
                                width: Sizer.width(70),
                                borderRadius: BorderRadius.circular(
                                  Sizer.radius(8),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ]),
                  );
                }

                //  Remove all category
                productRef.productCategories
                    .removeWhere((c) => c.category == 'all');
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(children: [
                    const XBox(16),
                    HomeTabOutline(
                      text: 'All',
                      isSelected: _categoryIndex == -1,
                      onTap: () async {
                        _categoryIndex = -1;
                        _selectedCategory = null;
                        _selectedExtraCategory = null;
                        setState(() {});
                        getProductsByCategory();
                      },
                    ),
                    ...List.generate(
                      productRef.productCategories.length,
                      (i) {
                        final c = productRef.productCategories[i];
                        return HomeTabOutline(
                          text: productCategoryHelper(c.category ?? '')
                              .categoryNmae,
                          isSelected: _categoryIndex == i,
                          onTap: () {
                            _categoryIndex = i;
                            _selectedCategory = c.category;
                            _selectedExtraCategory = null;
                            setState(() {});
                            getProductsByCategory(
                              category: c.category,
                            );
                          },
                        );
                      },
                    ),
                  ]),
                );
              }),
              const YBox(12),
              const HomeSlider(),
              const YBox(20),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(children: [
                  ..._extraCategoryMap.entries.map((entry) {
                    return HomeTabOutline(
                      text: entry.key,
                      isSelected: _selectedExtraCategory == entry.value,
                      onTap: () {
                        _selectedExtraCategory = entry.value;
                        setState(() {});
                        getProductsByCategory(
                          category: _selectedCategory,
                          extraCategory: entry.value,
                        );
                      },
                    );
                  }),
                ]),
              ),
              const YBox(20),
              LoadableContentBuilder(
                isBusy: productRef.busy(productsByCategoryFilter),
                items: productRef.productsByCaterory,
                loadingBuilder: (context) {
                  return GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                      bottom: Sizer.height(100),
                    ),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    childAspectRatio: 0.68,
                    children: List.generate(
                      10,
                      (i) => Skeletonizer(
                        enabled: true,
                        child: HomeProductCard(
                          productModel: ProductModel(
                            name: "Glenfiddich 18yrs",
                            volume: "75cl",
                            unitPrice: 379500,
                            category: "BEST SELLER",
                          ),
                        ),
                      ),
                    ),
                  );
                },
                emptyBuilder: (context) {
                  return SizedBox(
                      height: Sizer.height(220),
                      child: EmptyState(
                        text: "No products found",
                        btnText:
                            "Shop all ${productCategoryHelper(_selectedCategory ?? '').categoryNmae}",
                        btnWidth: Sizer.screenWidth * 0.55,
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.shopProductScreen,
                            arguments: _selectedCategory ?? '',
                          );
                        },
                      ));
                },
                contentBuilder: (context) {
                  // take 10 products
                  // final products = _selectedExtraCategory == "discount"
                  //     ? _productsByCaterory.toList()
                  //     : _productsByCaterory.take(10).toList();
                  return Column(
                    children: [
                      GridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.only(
                          left: Sizer.width(16),
                          right: Sizer.width(16),
                        ),
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        crossAxisCount: 2,
                        childAspectRatio: 0.64,
                        children: List.generate(
                          productRef.productsByCaterory.length,
                          (i) => HomeProductCard(
                            productModel: productRef.productsByCaterory[i],
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                RoutePath.productDetailsScreen,
                                arguments: productRef.productsByCaterory[i],
                              );
                            },
                          ),
                        ),
                      ),
                      if (productRef.busy(paginateState))
                        const SizerLoader(height: 100),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
