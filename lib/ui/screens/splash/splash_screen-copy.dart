import 'package:bottle_king_mobile/core/core.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late AnimationController _loadingAnimationController;

  late Animation<double> _logoFadeAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<Offset> _textSlideAnimation;
  late Animation<double> _loadingFadeAnimation;

  @override
  void initState() {
    super.initState();
    final authRef = ref.read(authVm);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LocationService.getUserLocationDetails(ref: ref);
    });

    _initializeAnimations();
    _startAnimationSequence();

    Future.delayed(const Duration(seconds: 4), () async {
      final userLoaded = await authRef.loadUserFromStorage();
      // final hasSeenOnboarding = await StorageService.getBoolItem(
      //       StorageKey.hasSeenOnboarding,
      //     ) ??
      //     false;
      final nextPage = userLoaded
          ? RoutePath.bottomNavScreen
          // : hasSeenOnboarding
          //     ? RoutePath.bottomNavScreen
          : RoutePath.onboardingScreen;

      // if (mounted) {
      //   Navigator.of(context).pushReplacementNamed(nextPage);
      // }
    });
  }

  void _initializeAnimations() {
    // Main animation controller for logo and text
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    // Loading animation controller
    _loadingAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Logo animations
    _logoFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _logoScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    // Text animations
    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.4, 0.8, curve: Curves.easeOut),
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.4, 0.8, curve: Curves.easeOut),
    ));

    // Loading indicator animation
    _loadingFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.6, 1.0, curve: Curves.easeIn),
    ));
  }

  void _startAnimationSequence() {
    _mainAnimationController.forward();

    // Start loading animation with a slight delay
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _loadingAnimationController.repeat();
      }
    });
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: _buildGradientBackground(),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo with scale and fade animation
                      AnimatedBuilder(
                        animation: _mainAnimationController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _logoScaleAnimation.value,
                            child: FadeTransition(
                              opacity: _logoFadeAnimation,
                              child: Container(
                                padding: EdgeInsets.all(Sizer.width(20)),
                                decoration: BoxDecoration(
                                  color: AppColors.white.withValues(alpha: 0.1),
                                  borderRadius:
                                      BorderRadius.circular(Sizer.radius(20)),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.black
                                          .withValues(alpha: 0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: imageHelper(
                                  AppImages.logo,
                                  height: Sizer.height(120),
                                  width: Sizer.width(120),
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      const YBox(32),

                      // App name with slide and fade animation
                      SlideTransition(
                        position: _textSlideAnimation,
                        child: FadeTransition(
                          opacity: _textFadeAnimation,
                          child: Column(
                            children: [
                              Text(
                                'Bottle King',
                                style: AppTypography.text36.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.white,
                                  letterSpacing: 1.2,
                                ),
                              ),
                              const YBox(8),
                              Text(
                                'Minister of Enjoyment',
                                style: AppTypography.text16.copyWith(
                                  color: AppColors.white.withValues(alpha: 0.8),
                                  fontWeight: FontWeight.w300,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Loading indicator at bottom
              FadeTransition(
                opacity: _loadingFadeAnimation,
                child: Padding(
                  padding: EdgeInsets.only(bottom: Sizer.height(60)),
                  child: Column(
                    children: [
                      _buildLoadingIndicator(),
                      const YBox(16),
                      Text(
                        'Loading your experience...',
                        style: AppTypography.text14.copyWith(
                          color: AppColors.white.withValues(alpha: 0.7),
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildGradientBackground() {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryBlack,
          AppColors.black,
          const Color(0xFF1A1A1A),
          AppColors.yellow37.withValues(alpha: 0.1),
        ],
        stops: const [0.0, 0.4, 0.8, 1.0],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedBuilder(
      animation: _loadingAnimationController,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            final delay = index * 0.2;
            final animationValue =
                (_loadingAnimationController.value - delay).clamp(0.0, 1.0);
            final scale = 0.5 +
                (0.5 * (1 - (animationValue - 0.5).abs() * 2).clamp(0.0, 1.0));

            return Container(
              margin: EdgeInsets.symmetric(horizontal: Sizer.width(4)),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: Sizer.width(8),
                  height: Sizer.width(8),
                  decoration: BoxDecoration(
                    color: AppColors.yellow37,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.yellow37.withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
