import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:card_swiper/card_swiper.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final SwiperController controller = SwiperController();
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Swiper(
            controller: controller,
            autoplay: true,
            autoplayDisableOnInteraction: true,
            autoplayDelay: 5000,
            duration: 1000,
            onIndexChanged: (i) {
              currentIndex = i;
              setState(() {});
            },
            itemBuilder: (ctx, i) {
              return Column(
                children: [
                  SizedBox(
                    height: Sizer.screenHeight * 0.6,
                    width: Sizer.screenWidth,
                    child: imageHelper(
                      AppText.onboardingList[i]['image'] ?? '',
                      fit: BoxFit.cover,
                    ),
                  ),
                ],
              );
            },
            itemCount: AppText.onboardingList.length,
          ),
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  imageHelper(
                    AppImages.logo2,
                    height: Sizer.height(32),
                    // fit: BoxFit.cover,
                  ),
                  const YBox(30),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          AppText.onboardingList[currentIndex]['title'] ?? '',
                          textAlign: TextAlign.center,
                          style: AppTypography.text18.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const YBox(12),
                        Text(
                          AppText.onboardingList[currentIndex]['description'] ??
                              '',
                          textAlign: TextAlign.center,
                          style: AppTypography.text15.copyWith(
                            color: AppColors.black70,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const YBox(70),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      AppText.onboardingList.length,
                      (i) => Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: Sizer.width(4)),
                        child: SwipeIndicator(
                          isActive: currentIndex == i,
                          onTap: () {
                            currentIndex = i;
                            controller.move(i);
                            setState(() {});
                          },
                        ),
                      ),
                    ),
                  ),
                  const YBox(24),
                  CustomBtn.solid(
                    onTap: () {
                      Navigator.pushNamed(context, RoutePath.welcomeScreen);
                    },
                    online: true,
                    text: "Join the party",
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
