import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late AnimationController _loadingAnimationController;

  late Animation<double> _logoFadeAnimation;
  late Animation<Offset> _logoDropAnimation;
  late Animation<double> _loadingFadeAnimation;

  @override
  void initState() {
    super.initState();
    final authRef = ref.read(authVm);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(appConfigVmodel).initialize();
    });

    _initializeAnimations();
    _startAnimationSequence();

    // Handle navigation after splash animation
    Future.delayed(const Duration(seconds: 4), () async {
      if (!mounted) return;

      try {
        final userLoaded = await authRef.loadUserFromStorage();

        // Get user location details
        await LocationService.getUserLocationDetails(ref: ref);

        // Check if app needs forced update
        if (ref.read(appConfigVmodel).appIsDueForUpdate &&
            EnvironmentConfig.isProduction) {
          if (!mounted) return;
          await Navigator.pushNamed(context, RoutePath.forceUpdateScreen);
          return;
        }

        // Determine next screen based on user auth status
        final nextPage =
            userLoaded ? RoutePath.bottomNavScreen : RoutePath.onboardingScreen;

        if (!mounted) return;
        await Navigator.pushReplacementNamed(context, nextPage);
      } catch (e) {
        // Handle any potential errors during navigation
        if (!mounted) return;
      }
    });
  }

  void _initializeAnimations() {
    // Main animation controller for logo drop and text
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    // Loading animation controller
    _loadingAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Logo drop animation - starts from above screen and settles below center
    _logoDropAnimation = Tween<Offset>(
      begin: const Offset(0, -1.5), // Start above screen
      end:
          const Offset(0, 0.4), // End slightly below center for settling effect
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.0, 0.8, curve: Curves.bounceOut),
    ));

    // Logo fade animation - fades in as it drops
    _logoFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeIn),
    ));

    // Loading indicator animation
    _loadingFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.8, 1.0, curve: Curves.easeIn),
    ));
  }

  void _startAnimationSequence() {
    _mainAnimationController.forward();

    // Start loading animation after logo settles
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        _loadingAnimationController.repeat();
      }
    });
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo with drop-and-bounce animation
                    SlideTransition(
                      position: _logoDropAnimation,
                      child: FadeTransition(
                        opacity: _logoFadeAnimation,
                        child: imageHelper(
                          AppImages.logo,
                          // height: Sizer.height(114),
                          width: Sizer.width(200),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),

                    // const YBox(40),

                    // // App name with slide and fade animation
                    // SlideTransition(
                    //   position: _textSlideAnimation,
                    //   child: FadeTransition(
                    //     opacity: _textFadeAnimation,
                    //     child: Column(
                    //       children: [
                    //         Lottie.asset('assets/images/wine.json',
                    //             height: Sizer.height(80)),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),

            // Loading indicator at bottom
            FadeTransition(
              opacity: _loadingFadeAnimation,
              child: Padding(
                padding: EdgeInsets.only(bottom: Sizer.height(40)),
                child: const LoaderIcon(
                  size: 40,
                  color: AppColors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
