import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:store_redirect/store_redirect.dart';

class ForceUpdateScreen extends ConsumerWidget {
  const ForceUpdateScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Align(
              child: Image.asset(
                AppImages.forceUpdate,
                height: Sizer.height(300),
              ),
            ),
            const YBox(160),
            Text(
              'New update is available',
              style: AppTypography.text18.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(8),
            Text(
              'A new version of Bottleking app is available. Please \nupdate to get the latest version',
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(32),
            CustomBtn.solid(
              text: "Update now",
              onTap: () async {
                await StoreRedirect.redirect(
                  androidAppId: "com.bottlekingng.bottlekingng",
                  iOSAppId: "6667120101",
                );
              },
            ),
            const YBox(12),
            Text(
              'Version ${ref.watch(appConfigVmodel).myAppCurrentVersion ?? ''}',
              textAlign: TextAlign.center,
              style: AppTypography.text12.copyWith(
                color: AppColors.black70,
              ),
            ),
            const YBox(50),
          ],
        ),
      ),
    );
  }
}
