import 'dart:async';

import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class SearchProductScreen extends ConsumerStatefulWidget {
  const SearchProductScreen({
    super.key,
    this.query,
  });

  final String? query;

  @override
  ConsumerState<SearchProductScreen> createState() =>
      _SearchProductScreenState();
}

class _SearchProductScreenState extends ConsumerState<SearchProductScreen> {
  final _searchC = TextEditingController();
  final _searchF = FocusNode();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;
  bool _hasSearched = false;
  String _lastSearchQuery = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    KeyboardOverlay.addRemoveFocusNode(context, _searchF);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchF.requestFocus();
      if (widget.query != null) {
        _searchC.text = widget.query!;
        _performSearch(widget.query!);
      }
    });
  }

  @override
  void dispose() {
    _searchC.dispose();
    _searchF.dispose();
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreResults();
    }
  }

  void _loadMoreResults() {
    final productRef = ref.read(productVm);
    if (!productRef.busy(paginateState) &&
        productRef.hasMoreSearchResults &&
        _lastSearchQuery.isNotEmpty) {
      productRef.productsBySearch(
        query: _lastSearchQuery,
        isFirstCall: false,
      );
    }
  }

  void _onSearchChanged(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // If query is empty, clear results immediately
    if (query.trim().isEmpty) {
      ref.read(productVm).clearSearchProducts();
      setState(() {
        _hasSearched = false;
        _lastSearchQuery = '';
      });
      return;
    }

    // Set up debounce timer for 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.trim() != _lastSearchQuery) {
        _performSearch(query.trim());
      }
    });
  }

  void _performSearch(String query) {
    if (query.isEmpty) return;

    setState(() {
      _hasSearched = true;
      _lastSearchQuery = query;
    });

    ref.read(productVm).productsBySearch(query: query, isFirstCall: true);
  }

  void _clearSearch() {
    _searchC.clear();
    _searchF.unfocus();
    _debounceTimer?.cancel();
    ref.read(productVm).clearSearchProducts();
    setState(() {
      _hasSearched = false;
      _lastSearchQuery = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(Sizer.height(60)),
        child: Container(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black12.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      productRef.clearSearchProducts();
                    },
                    child: Padding(
                      padding: EdgeInsets.all(Sizer.radius(4)),
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: Sizer.height(20),
                        color: AppColors.black70,
                      ),
                    ),
                  ),
                  const XBox(8),
                  Expanded(
                    child: CustomTextField(
                      controller: _searchC,
                      focusNode: _searchF,
                      hintText: 'Find your favourite drinks',
                      fillColor: AppColors.transparent,
                      hideBorder: true,
                      borderRadius: 0,
                      onChanged: _onSearchChanged,
                    ),
                  ),
                  InkWell(
                    onTap: _clearSearch,
                    child: Padding(
                      padding: EdgeInsets.all(Sizer.radius(4)),
                      child: Icon(
                        Icons.close,
                        size: Sizer.height(20),
                        color: AppColors.black70,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: LoadableContentBuilder(
                isBusy: productRef.busy(productSearchState),
                isError: productRef.error(productSearchState),
                items: productRef.searchProducts,
                loadingBuilder: (context) {
                  return GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                      bottom: Sizer.height(100),
                    ),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    childAspectRatio: 0.68,
                    children: List.generate(
                      6,
                      (i) => Skeletonizer(
                        enabled: true,
                        child: HomeProductCard(
                          productModel: ProductModel(
                            name: "Loading product...",
                            volume: "75cl",
                            unitPrice: 0,
                            category: "LOADING",
                          ),
                        ),
                      ),
                    ),
                  );
                },
                emptyBuilder: (context) {
                  return _buildEmptyState();
                },
                errorBuilder: (context) {
                  return _buildErrorState();
                },
                contentBuilder: (context) {
                  return RefreshIndicator(
                    onRefresh: () async {
                      if (_lastSearchQuery.isNotEmpty) {
                        await ref
                            .read(productVm)
                            .productsBySearch(query: _lastSearchQuery);
                      }
                    },
                    child: CustomScrollView(
                      controller: _scrollController,
                      slivers: [
                        SliverPadding(
                          padding: EdgeInsets.only(
                            left: Sizer.width(16),
                            right: Sizer.width(16),
                          ),
                          sliver: SliverGrid(
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                              childAspectRatio: 0.64,
                            ),
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final sp = productRef.searchProducts[index];
                                return HomeProductCard(
                                  productModel: sp,
                                  onTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      RoutePath.productDetailsScreen,
                                      arguments: sp,
                                    );
                                  },
                                );
                              },
                              childCount: productRef.searchProducts.length,
                            ),
                          ),
                        ),
                        // Loading indicator for pagination
                        if (productRef.busy(paginateState))
                          SliverToBoxAdapter(
                            child: Container(
                              padding: EdgeInsets.all(Sizer.height(20)),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                          ),
                        // Bottom spacing
                        SliverToBoxAdapter(
                          child: SizedBox(height: Sizer.height(100)),
                        ),
                      ],
                    ),
                  );
                }),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    if (!_hasSearched) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: Sizer.height(80),
              color: AppColors.grayDD,
            ),
            const YBox(16),
            Text(
              "Start typing to search for products",
              style: AppTypography.text18.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.black70,
              ),
              textAlign: TextAlign.center,
            ),
            const YBox(8),
            Text(
              "Find your favorite drinks and beverages",
              style: AppTypography.text14.copyWith(
                color: AppColors.gray500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: Sizer.height(80),
              color: AppColors.grayDD,
            ),
            const YBox(16),
            Text(
              "No products found",
              style: AppTypography.text18.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.black70,
              ),
              textAlign: TextAlign.center,
            ),
            const YBox(8),
            Text(
              "Try searching with different keywords",
              style: AppTypography.text14.copyWith(
                color: AppColors.gray500,
              ),
              textAlign: TextAlign.center,
            ),
            const YBox(24),
            InkWell(
              onTap: _clearSearch,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                  vertical: Sizer.height(12),
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.primaryBlack),
                  borderRadius: BorderRadius.circular(Sizer.radius(8)),
                ),
                child: Text(
                  "Clear Search",
                  style: AppTypography.text14.copyWith(
                    color: AppColors.primaryBlack,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: Sizer.height(80),
            color: AppColors.red,
          ),
          const YBox(16),
          Text(
            "Something went wrong",
            style: AppTypography.text18.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.black70,
            ),
            textAlign: TextAlign.center,
          ),
          const YBox(8),
          Text(
            "Please check your connection and try again",
            style: AppTypography.text14.copyWith(
              color: AppColors.gray500,
            ),
            textAlign: TextAlign.center,
          ),
          const YBox(24),
          InkWell(
            onTap: () {
              if (_lastSearchQuery.isNotEmpty) {
                ref.read(productVm).productsBySearch(
                    query: _lastSearchQuery, isFirstCall: true);
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
                vertical: Sizer.height(12),
              ),
              decoration: BoxDecoration(
                color: AppColors.primaryBlack,
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
              ),
              child: Text(
                "Try Again",
                style: AppTypography.text14.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
