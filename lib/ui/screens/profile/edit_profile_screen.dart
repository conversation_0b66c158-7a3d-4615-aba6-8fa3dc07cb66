import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:flutter/gestures.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final fullnameC = TextEditingController();
  final phoneC = TextEditingController();
  final emailC = TextEditingController();
  final birthdayC = TextEditingController();

  DateTime? _selectedDOB;

  // Store original values for comparison
  String _originalFullname = "";
  String _originalPhone = "";
  String _originalEmail = "";
  DateTime? _originalDOB;

  /// Check if user is at least 18 years old
  /// Uses the centralized AppUtils validation method
  bool _isValidAge(DateTime birthDate) => AppUtils.isValidAge(birthDate);

  /// Check if date of birth is already set (not editable)
  bool get _isDOBSet => _originalDOB != null;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initSet();
    });
  }

  _initSet() {
    final authRef = ref.watch(authVm);

    // Set current values
    fullnameC.text = authRef.fullNames;
    phoneC.text = authRef.user?.phone ?? "";
    emailC.text = authRef.user?.email ?? "";
    birthdayC.text = authRef.user?.dateOfBirth != null
        ? AppUtils.dayWithSuffixMonthAndYear(authRef.user!.dateOfBirth!)
        : "";
    _selectedDOB = authRef.user?.dateOfBirth;

    // Store original values for comparison
    _originalFullname = authRef.fullNames;
    _originalPhone = authRef.user?.phone ?? "";
    _originalEmail = authRef.user?.email ?? "";
    _originalDOB = authRef.user?.dateOfBirth;

    setState(() {});
  }

  // Check if any profile data has been modified
  bool get _hasChanges {
    final currentFullname = fullnameC.text.trim();
    final currentPhone = phoneC.text.trim();
    final currentEmail = emailC.text.trim();

    return currentFullname != _originalFullname ||
        currentPhone != _originalPhone ||
        currentEmail != _originalEmail ||
        _selectedDOB != _originalDOB;
  }

  @override
  void dispose() {
    fullnameC.dispose();
    phoneC.dispose();
    emailC.dispose();
    birthdayC.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Edit profile",
      ),
      body: Builder(builder: (context) {
        if (authRef.isBusy) {
          return const SizerLoader(
            height: double.infinity,
          );
        }

        return ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            const YBox(24),
            CustomTextField(
              controller: fullnameC,
              labelText: "Full name",
              // fillColor: AppColors.grayF6,
              showLabelHeader: true,
              borderRadius: 0,
            ),
            const YBox(20),
            CustomTextField(
              controller: phoneC,
              labelText: "Phone number",
              fillColor: AppColors.grayF6,
              textfieldColor: AppColors.gray75,
              showLabelHeader: true,
              borderRadius: 0,
              isReadOnly: true,
            ),
            // RichText(
            //   text: TextSpan(
            //     style: AppTypography.text12.copyWith(
            //       color: AppColors.black70,
            //     ),
            //     children: [
            //       const TextSpan(
            //         text: "Phone cannot be changed, ",
            //       ),
            //       TextSpan(
            //         text: "Contact support",
            //         style: AppTypography.text12.copyWith(
            //           color: AppColors.black70,
            //           fontWeight: FontWeight.bold,
            //           decoration: TextDecoration.underline,
            //         ),
            //         recognizer: TapGestureRecognizer()
            //           ..onTap = () {
            //             Navigator.pushNamed(context, RoutePath.supportScreen,
            //                 arguments: 1);
            //           },
            //       ),
            //       const TextSpan(
            //         text: " to update",
            //       ),
            //     ],
            //   ),
            // ),
            const YBox(20),
            CustomTextField(
              controller: emailC,
              labelText: "Email",
              fillColor: AppColors.grayF6,
              textfieldColor: AppColors.gray75,
              showLabelHeader: true,
              borderRadius: 0,
              isReadOnly: true,
            ),
            const YBox(4),
            // RichText(
            //   text: TextSpan(
            //     style: AppTypography.text12.copyWith(
            //       color: AppColors.black70,
            //     ),
            //     children: [
            //       const TextSpan(
            //         text: "Email cannot be changed, ",
            //       ),
            //       TextSpan(
            //         text: "Contact support",
            //         style: AppTypography.text12.copyWith(
            //           color: AppColors.black70,
            //           fontWeight: FontWeight.bold,
            //           decoration: TextDecoration.underline,
            //         ),
            //         recognizer: TapGestureRecognizer()
            //           ..onTap = () {
            //             Navigator.pushNamed(context, RoutePath.supportScreen,
            //                 arguments: 1);
            //           },
            //       ),
            //       const TextSpan(
            //         text: " to update",
            //       ),
            //     ],
            //   ),
            // ),
            const YBox(20),
            CustomTextField(
              controller: birthdayC,
              hintText: _isDOBSet ? null : "You must be 18 years and above",
              labelText: "Date of birth",
              showLabelHeader: true,
              isReadOnly: true,
              borderRadius: 0,
              suffixIcon: _isDOBSet
                  ? null
                  : Icon(
                      Iconsax.calendar_1,
                      size: Sizer.height(24),
                      color: AppColors.black70,
                    ),
              onTap: _isDOBSet
                  ? null
                  : () {
                      CustomCupertinoDatePicker(
                        context: context,
                        initialDateTime: _selectedDOB ??
                            DateTime.now().subtract(const Duration(days: 6570)),
                        maximumDate:
                            DateTime.now().subtract(const Duration(days: 6570)),
                        onDateTimeChanged: (dateTime) {
                          _selectedDOB = dateTime;
                        },
                        onDone: () {
                          // Validate age with proper calculation
                          if (_selectedDOB != null) {
                            if (!_isValidAge(_selectedDOB!)) {
                              FlushBarToast.fLSnackBar(
                                snackBarType: SnackBarType.warning,
                                message:
                                    "You must be 18 years and above to register",
                              );
                              _selectedDOB = null;
                              setState(() {});
                            } else {
                              birthdayC.text =
                                  AppUtils.dayWithSuffixMonthAndYear(
                                      _selectedDOB ?? DateTime.now());
                              setState(() {});
                            }
                          }
                          Navigator.pop(context);
                        },
                      ).show();
                    },
            ),
            // Show support message only when date of birth is set
            if (_isDOBSet) ...[
              const YBox(4),
              RichText(
                text: TextSpan(
                  style: AppTypography.text12.copyWith(
                    color: AppColors.black70,
                  ),
                  children: [
                    const TextSpan(
                      text: "Date of birth cannot be changed, ",
                    ),
                    TextSpan(
                      text: "Contact support",
                      style: AppTypography.text12.copyWith(
                        color: AppColors.black70,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Navigator.pushNamed(context, RoutePath.supportScreen,
                              arguments: 1);
                        },
                    ),
                    const TextSpan(
                      text: " to update",
                    ),
                  ],
                ),
              ),
            ],
            const YBox(140),
            CustomBtn.solid(
              isOutline: true,
              textColor: AppColors.primaryBlack,
              onTap: () async {
                // Check if user made any modifications
                if (!_hasChanges) {
                  FlushBarToast.fLSnackBar(
                    snackBarType: SnackBarType.info,
                    message:
                        "No changes detected. Please modify your profile data before saving.",
                  );
                  return;
                }

                final authRef = ref.read(authVm);
                final res = await authRef.updateProfile(
                    dateOfBirth: _selectedDOB?.toIso8601String());

                handleApiResponse(
                    response: res,
                    onSuccess: () {
                      // Update original values after successful save
                      _originalFullname = fullnameC.text.trim();
                      _originalPhone = phoneC.text.trim();
                      _originalEmail = emailC.text.trim();
                      _originalDOB = _selectedDOB;
                      setState(() {});
                    });
              },
              online: true,
              text: "Save",
            ),
            const YBox(24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {
                    // Navigator.pushNamed(context, RoutePath.deleteAccountScreen);

                    final loadingProvider = StateProvider<bool>((ref) => false);

                    ModalWrapper.bottomSheet(
                      context: context,
                      widget: Consumer(
                        builder: (context, ref, child) {
                          final isLoading = ref.watch(loadingProvider);

                          return ConfirmModal(
                            title: "Delete profile",
                            subtitle:
                                "Are you sure you want to delete your profile",
                            rightBtnText: "Delete",
                            isLoading: isLoading,
                            rightBtnTap: () async {
                              // Set loading to true

                              ref.read(loadingProvider.notifier).state = true;

                              try {} finally {
                                if (context.mounted) {
                                  ref.read(loadingProvider.notifier).state =
                                      false;
                                  Navigator.pop(context);
                                }
                              }
                            },
                          );
                        },
                      ),
                    );
                  },
                  child: Text(
                    "Delete account",
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.red15,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      }),
    );
  }
}
