import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Wallet",
      ),
      body: 1 + 1 == 2
          ? Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Coming Soon",
                    style: AppTypography.text18,
                  ),
                ],
              ),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const YBox(20),
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(20),
                    vertical: Sizer.height(24),
                  ),
                  decoration: const BoxDecoration(
                    color: AppColors.black,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Available balance",
                              style: AppTypography.text15.copyWith(
                                fontWeight: FontWeight.w500,
                                color: AppColors.white,
                              ),
                            ),
                            const YBox(4),
                            Text(
                              "NGN 0",
                              style: AppTypography.text24.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(12),
                          vertical: Sizer.height(8),
                        ),
                        color: AppColors.white,
                        child: Text(
                          "Top up wallet",
                          style: AppTypography.text14.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                const YBox(26),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Manage cards",
                        style: AppTypography.text16.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: Sizer.height(16),
                        color: AppColors.black70,
                      )
                    ],
                  ),
                ),
                const YBox(26),
                const HLine(),
                const YBox(16),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
                  child: Text(
                    "Recent transactions",
                    style: AppTypography.text18.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
