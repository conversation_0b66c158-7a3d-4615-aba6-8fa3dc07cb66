import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class WishlistScreen extends ConsumerStatefulWidget {
  const WishlistScreen({super.key});

  @override
  ConsumerState<WishlistScreen> createState() => _WishlistScreenState();
}

class _WishlistScreenState extends ConsumerState<WishlistScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartVm.notifier).getWishlist();
    });
  }

  @override
  Widget build(BuildContext context) {
    final cartRef = ref.watch(cartVm);
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Wishlist",
      ),
      body: LoadableContentBuilder(
        isBusy: cartRef.busy(getWishlistState),
        items: cartRef.wishList,
        loadingBuilder: (context) {
          return const SizerLoader(height: 700);
        },
        emptyBuilder: (context) {
          return EmptyState(
            text: "No items in wishlist",
            btnText: "Start shopping",
            onTap: () {
              Navigator.pushNamed(
                context,
                RoutePath.bottomNavScreen,
                arguments: DashArg(index: 1),
              );
            },
          );
        },
        contentBuilder: (context) {
          return ListView.separated(
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              top: Sizer.width(16),
              bottom: Sizer.width(100),
            ),
            itemBuilder: (ctx, i) {
              final w = cartRef.wishList[i];
              return CartProductCard(
                showRemoveBtn: true,
                cartItem: w,
                onRemoveTap: () {
                  printty("remove from wishlist: ${w.productId}");
                  final loadingProvider = StateProvider<bool>((ref) => false);

                  ModalWrapper.bottomSheet(
                    context: context,
                    widget: Consumer(
                      builder: (context, ref, child) {
                        final isLoading = ref.watch(loadingProvider);

                        return ConfirmModal(
                          title: "Remove from wishlist",
                          subtitle:
                              "Are you sure you want to remove this item from wishlist",
                          rightBtnText: "Remove",
                          isLoading: isLoading,
                          rightBtnTap: () async {
                            // Set loading to true
                            ref.read(loadingProvider.notifier).state = true;

                            try {
                              await ref
                                  .read(cartVm.notifier)
                                  .removeCartOrWishlist(
                                    itemId: w.id ?? "",
                                    isWishList: true,
                                  );
                            } finally {
                              if (context.mounted) {
                                ref.read(loadingProvider.notifier).state =
                                    false;
                                Navigator.pop(context);
                              }
                            }
                          },
                        );
                      },
                    ),
                  );
                },
              );
            },
            separatorBuilder: (_, __) => Padding(
              padding: EdgeInsets.symmetric(
                vertical: Sizer.height(10),
              ),
              child: const Divider(color: AppColors.grayE6),
            ),
            itemCount: cartRef.wishList.length,
          );
        },
      ),
    );
  }
}
