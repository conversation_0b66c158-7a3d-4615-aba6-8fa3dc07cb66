import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Notifications",
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(20),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Row(
              children: [
                Text(
                  "Select all notifications",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                CustomSwitch(
                  value: true,
                  onChanged: (value) {},
                ),
              ],
            ),
          ),
          const YBox(20),
          const HLine(),
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              children: [
                NotificationTile(
                  title: "Order updates",
                  subtitle:
                      "We will keep you updated on the status of your order",
                  isActive: true,
                  onChanged: (v) {},
                ),
                const YBox(24),
                NotificationTile(
                  title: "Stock alerts",
                  subtitle:
                      "Get heads up when new products are added or when your favourite product is back in store",
                  isActive: false,
                  onChanged: (v) {},
                ),
                const YBox(24),
                NotificationTile(
                  title: "Exclusive discounts",
                  subtitle:
                      "Be the first in line to grab the drinks you love for less, get exclusive discounts, new drops and personalized offers",
                  isActive: false,
                  onChanged: (v) {},
                ),
              ],
            ),
          ),
          const YBox(30),
          const HLine(),
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Text(
              "Changing your preference will update all devices that you are logged in on",
              textAlign: TextAlign.center,
              style: AppTypography.text12.copyWith(
                color: AppColors.black70,
              ),
            ),
          )
        ],
      ),
    );
  }
}
