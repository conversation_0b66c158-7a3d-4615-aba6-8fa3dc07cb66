import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class AllAddressScreen extends ConsumerStatefulWidget {
  const AllAddressScreen({
    super.key,
    required this.isHomeAddressChange,
  });

  final bool isHomeAddressChange;

  @override
  ConsumerState<AllAddressScreen> createState() => _AllAddressScreenState();
}

class _AllAddressScreenState extends ConsumerState<AllAddressScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(addressVm.notifier).getAddresses();
    });
  }

  AddressModel? _selectedAddress;

  @override
  Widget build(BuildContext context) {
    final addRef = ref.watch(addressVm);
    return BusyOverlay(
      show: addRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "All addresses",
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: LoadableContentBuilder(
                  isBusy: addRef.busy(getAddressState),
                  items: addRef.addresses,
                  loadingBuilder: (context) {
                    return const SizerLoader();
                  },
                  emptyBuilder: (context) {
                    return Center(
                      child: Text(
                        "No address found",
                        style: AppTypography.text18.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.black70),
                      ),
                    );
                  },
                  contentBuilder: (context) {
                    return RefreshIndicator(
                      onRefresh: () async {
                        ref.read(addressVm.notifier).getAddresses(false);
                      },
                      child: ListView.separated(
                        padding: EdgeInsets.only(
                          top: Sizer.height(20),
                          bottom: Sizer.height(100),
                        ),
                        shrinkWrap: true,
                        itemBuilder: (ctx, i) {
                          final a = addRef.addresses[i];
                          return InkWell(
                            onTap: () {
                              setState(() {
                                _selectedAddress = a;
                              });
                            },
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: Sizer.height(4)),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      a.fullAddress ?? "",
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: AppTypography.text14.copyWith(),
                                    ),
                                  ),
                                  const XBox(20),
                                  if (!widget.isHomeAddressChange)
                                    InkWell(
                                      onTap: () {
                                        // Create a StateProvider to track loading state just for this modal
                                        final loadingProvider =
                                            StateProvider<bool>((ref) => false);

                                        ModalWrapper.bottomSheet(
                                          context: context,
                                          widget: Consumer(
                                            builder: (context, ref, child) {
                                              final isLoading =
                                                  ref.watch(loadingProvider);

                                              return ConfirmModal(
                                                title: "Delete address",
                                                subtitle:
                                                    "Are you sure you want to delete this address",
                                                rightBtnText: "Delete",
                                                isLoading: isLoading,
                                                rightBtnTap: () async {
                                                  // Set loading to true
                                                  printty(
                                                      "delete address: ${a.id}");
                                                  ref
                                                      .read(loadingProvider
                                                          .notifier)
                                                      .state = true;

                                                  try {
                                                    await ref
                                                        .read(
                                                            addressVm.notifier)
                                                        .deleteAddresses(
                                                            a.id ?? "");
                                                    ref
                                                        .read(
                                                            addressVm.notifier)
                                                        .getAddresses(false);
                                                  } finally {
                                                    if (context.mounted) {
                                                      ref
                                                          .read(loadingProvider
                                                              .notifier)
                                                          .state = false;
                                                      Navigator.pop(context);
                                                    }
                                                  }
                                                },
                                              );
                                            },
                                          ),
                                        );
                                      },
                                      child: SvgPicture.asset(
                                        AppSvgs.circleDelete,
                                        height: Sizer.height(32),
                                      ),
                                    ),
                                  if (widget.isHomeAddressChange)
                                    Icon(
                                      Icons.check_circle,
                                      color: _selectedAddress == a
                                          ? AppColors.primaryBlack
                                          : Colors.transparent,
                                      size: Sizer.height(20),
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (_, __) => Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: Sizer.height(10),
                          ),
                          child: const Divider(
                              thickness: 1, color: AppColors.grayE6),
                        ),
                        itemCount: addRef.addresses.length,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        bottomSheet: !widget.isHomeAddressChange
            ? null
            : Container(
                padding: EdgeInsets.only(
                  left: Sizer.width(16),
                  right: Sizer.width(16),
                  top: Sizer.height(5),
                  bottom: Sizer.height(30),
                ),
                color: Colors.white,
                child: CustomBtn.solid(
                  onTap: () async {
                    final res = await ref
                        .read(addressVm)
                        .setAddressAsDefault(id: _selectedAddress!.id ?? "");

                    handleApiResponse(
                      response: res,
                      onSuccess: () {
                        ref.read(addressVm.notifier).getAddresses(false);
                        Navigator.pop(context);
                      },
                    );
                  },
                  online: _selectedAddress != null,
                  text: "Set as home address",
                ),
              ),
      ),
    );
  }
}
