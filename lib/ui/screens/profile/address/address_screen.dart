import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class AddressScreen extends ConsumerStatefulWidget {
  const AddressScreen({super.key});

  @override
  ConsumerState<AddressScreen> createState() => _AddressScreenState();
}

class _AddressScreenState extends ConsumerState<AddressScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(addressVm.notifier).getAddresses();
    });
  }

  @override
  Widget build(BuildContext context) {
    final addRef = ref.watch(addressVm);
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Addresses",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(30),
            AddressCard(
              title: "Selected address",
              subtitle: "Current location",
              address: addRef.currentAddress?.fullAddress ?? "",
            ),
            const YBox(16),
            AddressCard(
              title: "Home address",
              subtitle: "Home",
              address: addRef.homeAddress?.fullAddress ?? "",
              trailingWidget: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.addAddressScreen);
                },
                child: Text(
                  "Add address",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const YBox(24),
            Text(
              "Addresses",
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(6),
            Expanded(
              child: LoadableContentBuilder(
                isBusy: addRef.busy(getAddressState),
                items: addRef.addresses,
                loadingBuilder: (context) {
                  return const SizerLoader();
                },
                emptyBuilder: (context) {
                  return Center(
                    child: Text(
                      "No address found",
                      style: AppTypography.text18.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.black70),
                    ),
                  );
                },
                contentBuilder: (context) {
                  return RefreshIndicator(
                    onRefresh: () async {
                      ref.read(addressVm.notifier).getAddresses(false);
                    },
                    child: ListView.separated(
                      padding: EdgeInsets.only(
                        top: Sizer.height(20),
                        bottom: Sizer.height(100),
                      ),
                      shrinkWrap: true,
                      itemBuilder: (ctx, i) {
                        final a = addRef.addresses[i];
                        return Padding(
                          padding:
                              EdgeInsets.symmetric(vertical: Sizer.height(4)),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  a.fullAddress ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppTypography.text14.copyWith(),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  // Create a StateProvider to track loading state just for this modal
                                  final loadingProvider =
                                      StateProvider<bool>((ref) => false);

                                  ModalWrapper.bottomSheet(
                                    context: context,
                                    widget: Consumer(
                                      builder: (context, ref, child) {
                                        final isLoading =
                                            ref.watch(loadingProvider);

                                        return ConfirmModal(
                                          title: "Delete address",
                                          subtitle:
                                              "Are you sure you want to delete this address",
                                          rightBtnText: "Delete",
                                          isLoading: isLoading,
                                          rightBtnTap: () async {
                                            // Set loading to true
                                            printty("delete address: ${a.id}");
                                            ref
                                                .read(loadingProvider.notifier)
                                                .state = true;

                                            try {
                                              await ref
                                                  .read(addressVm.notifier)
                                                  .deleteAddresses(a.id ?? "");
                                              ref
                                                  .read(addressVm.notifier)
                                                  .getAddresses(false);
                                            } finally {
                                              if (context.mounted) {
                                                ref
                                                    .read(loadingProvider
                                                        .notifier)
                                                    .state = false;
                                                Navigator.pop(context);
                                              }
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  );
                                },
                                child: SvgPicture.asset(
                                  AppSvgs.circleDelete,
                                  height: Sizer.height(32),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      separatorBuilder: (_, __) => Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: Sizer.height(10),
                        ),
                        child: const Divider(
                            thickness: 1, color: AppColors.grayE6),
                      ),
                      itemCount: addRef.addresses.length,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddressCard extends StatelessWidget {
  const AddressCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.address,
    this.trailingWidget,
  });

  final String title;
  final String subtitle;
  final String address;
  final Widget? trailingWidget;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Sizer.width(16)),
      decoration: const BoxDecoration(
        color: AppColors.greyF7,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                title,
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Container(
                child: trailingWidget,
              )
            ],
          ),
          const YBox(12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(
                AppSvgs.location,
                colorFilter: const ColorFilter.mode(
                  AppColors.black70,
                  BlendMode.srcIn,
                ),
              ),
              const XBox(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subtitle,
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const YBox(8),
                    Text(
                      address,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
