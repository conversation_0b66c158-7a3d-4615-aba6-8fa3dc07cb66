import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key, this.tabIndex});

  final int? tabIndex;

  @override
  State<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.tabIndex != null) {
        _tabController.animateTo(widget.tabIndex!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Support",
      ),
      body: Padding(
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(10),
            Container(
              height: Sizer.height(117),
              decoration: const BoxDecoration(
                  image: DecorationImage(
                image: AssetImage(AppImages.support),
              )),
            ),
            const YBox(20),
            const HLine(),
            const YBox(16),
            Container(
              height: Sizer.height(42),
              width: Sizer.screenWidth,
              decoration: BoxDecoration(
                color: AppColors.greyF7,
                // borderRadius: BorderRadius.circular(Sizer.radius(12)),
                border: Border.all(width: 1, color: AppColors.grayF0),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(6),
                vertical: Sizer.height(4),
              ),
              child: TabBar(
                splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                physics: const NeverScrollableScrollPhysics(),
                onTap: (int value) {},
                dividerColor: Colors.transparent,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(Sizer.radius(8)),
                  color: AppColors.white,
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: AppColors.primaryBlack,
                automaticIndicatorColorAdjustment: true,
                labelStyle: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                unselectedLabelStyle: AppTypography.text14.copyWith(
                  color: AppColors.black70,
                  fontWeight: FontWeight.w500,
                ),
                controller: _tabController,
                tabs: const [
                  Tab(text: 'FAQ'),
                  Tab(text: 'Contact us'),
                ],
              ),
            ),
            const YBox(20),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  FaqTabView(),
                  ContactTabView(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
