import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class OrderScreen extends ConsumerStatefulWidget {
  const OrderScreen({super.key});

  @override
  ConsumerState<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends ConsumerState<OrderScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(Sizer.height(120)),
        child: Container(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
            bottom: Sizer.width(16),
          ),
          decoration: const BoxDecoration(
              border: Border(
            bottom: BorderSide(
              width: 3,
              color: AppColors.greyF7,
            ),
          )),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Orders',
                style: AppTypography.text18.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const YBox(16),
              Container(
                height: Sizer.height(42),
                width: Sizer.screenWidth,
                decoration: BoxDecoration(
                  color: AppColors.greyF7,
                  // borderRadius: BorderRadius.circular(Sizer.radius(12)),
                  border: Border.all(width: 1, color: AppColors.grayF0),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(6),
                  vertical: Sizer.height(4),
                ),
                child: TabBar(
                  splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                  physics: const NeverScrollableScrollPhysics(),
                  onTap: (int value) {
                    setState(() {
                      selectedIndex = value;
                    });
                  },
                  dividerColor: Colors.transparent,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(Sizer.radius(8)),
                    color: AppColors.white,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelColor: AppColors.primaryBlack,
                  automaticIndicatorColorAdjustment: true,
                  labelStyle: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  unselectedLabelStyle: AppTypography.text14.copyWith(
                    color: AppColors.black70,
                    fontWeight: FontWeight.w500,
                  ),
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'All orders'),
                    Tab(text: 'Ongoing'),
                    Tab(text: 'Delivered'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          AllOrdersTab(),
          OngoingTab(),
          DeliveredTab(),
        ],
      ),
    );
  }
}
