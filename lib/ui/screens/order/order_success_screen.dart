import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:lottie/lottie.dart';

class OrderSuccessScreen extends StatefulWidget {
  const OrderSuccessScreen({super.key});

  @override
  State<OrderSuccessScreen> createState() => _OrderSuccessScreenState();
}

class _OrderSuccessScreenState extends State<OrderSuccessScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset(
              'assets/svgs/success.json',
              height: Sizer.height(150),
              width: Sizer.width(150),
            ),
            const YBox(60),
            CustomBtn.solid(
              onTap: () {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  RoutePath.bottomNavScreen,
                  (_) => false,
                  arguments: DashArg(index: 1),
                );
              },
              online: true,
              isOutline: true,
              outlineColor: AppColors.blackBD,
              textColor: AppColors.primaryBlack,
              text: "Continue shopping",
            ),
            const YBox(20),
            CustomBtn.solid(
              onTap: () async {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  RoutePath.bottomNavScreen,
                  (_) => false,
                  arguments: DashArg(index: 2),
                );
              },
              online: true,
              text: "View Order",
            ),
            const YBox(10),
          ],
        ),
      ),
    );
  }
}
