import 'dart:io';
import 'dart:ui' as ui;

import 'package:bottle_king_mobile/lib.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:gal/gal.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;

class ReceiptScreen extends StatefulWidget {
  const ReceiptScreen({super.key, required this.order});

  final OrderDetailsModel order;

  @override
  State<ReceiptScreen> createState() => _ReceiptScreenState();
}

class _ReceiptScreenState extends State<ReceiptScreen> {
  final GlobalKey _receiptKey = GlobalKey();
  String _format = 'PNG';
  bool _isExporting = false;

  Future<Uint8List> _captureReceiptPngBytes({double pixelRatio = 3.0}) async {
    final boundary = _receiptKey.currentContext?.findRenderObject()
        as RenderRepaintBoundary?;
    final uiImage = await boundary?.toImage(pixelRatio: pixelRatio);
    final byteData = await uiImage?.toByteData(format: ui.ImageByteFormat.png);
    return byteData?.buffer.asUint8List() ?? Uint8List(0);
  }

  Future<File> _writeTempFile(String filename, List<int> bytes) async {
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/$filename');
    await file.writeAsBytes(bytes, flush: true);
    return file;
  }

  Future<void> _download() async {
    setState(() => _isExporting = true);
    try {
      if (_format == 'PNG') {
        final pngBytes = await _captureReceiptPngBytes(pixelRatio: 3.0);
        if (pngBytes.isEmpty) throw Exception('Failed to generate image');

        try {
          final hasAlbumAccess = await Gal.hasAccess(toAlbum: true);
          if (!hasAlbumAccess) {
            await Gal.requestAccess(toAlbum: true);
          }

          await Gal.putImageBytes(pngBytes, album: 'Bottle King');
          FlushBarToast.fLSnackBar(
            snackBarType: SnackBarType.success,
            message: 'Receipt saved to gallery',
          );
        } on MissingPluginException {
          FlushBarToast.fLSnackBar(
            snackBarType: SnackBarType.warning,
            message:
                'Gallery download unavailable. Please fully rebuild the app to register plugins.',
          );
        }
      } else {
        final pngBytes = await _captureReceiptPngBytes(pixelRatio: 3.0);
        if (pngBytes.isEmpty) throw Exception('Failed to generate image');

        final pdf = pw.Document();
        final image = pw.MemoryImage(pngBytes);
        pdf.addPage(
          pw.Page(
            margin: const pw.EdgeInsets.all(24),
            build: (context) => pw.Center(
              child: pw.Image(image, fit: pw.BoxFit.contain),
            ),
          ),
        );
        final bytes = await pdf.save();
        final file =
            await _writeTempFile('receipt_${widget.order.orderId}.pdf', bytes);
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.success,
          message: 'Receipt saved: ${file.path}',
        );
      }
    } catch (e) {
      printty("Error downloading receipt: $e");
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: e.toString(),
      );
    } finally {
      if (mounted) setState(() => _isExporting = false);
    }
  }

  Future<void> _share() async {
    setState(() => _isExporting = true);
    try {
      if (_format == 'PNG') {
        final pngBytes = await _captureReceiptPngBytes(pixelRatio: 3.0);
        if (pngBytes.isEmpty) throw Exception('Failed to generate image');
        final file = await _writeTempFile(
            'receipt_${widget.order.orderId}.png', pngBytes);
        await SharePlus.instance.share(
            ShareParams(files: [XFile(file.path)], text: 'Order receipt'));
      } else {
        final pngBytes = await _captureReceiptPngBytes(pixelRatio: 3.0);
        if (pngBytes.isEmpty) throw Exception('Failed to generate image');
        final pdf = pw.Document();
        final image = pw.MemoryImage(pngBytes);
        pdf.addPage(
          pw.Page(
            margin: const pw.EdgeInsets.all(24),
            build: (context) => pw.Center(
              child: pw.Image(image, fit: pw.BoxFit.contain),
            ),
          ),
        );
        final bytes = await pdf.save();
        final file =
            await _writeTempFile('receipt_${widget.order.orderId}.pdf', bytes);
        await SharePlus.instance.share(
            ShareParams(files: [XFile(file.path)], text: 'Order receipt'));
      }
    } catch (e) {
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: e.toString(),
      );
    } finally {
      if (mounted) setState(() => _isExporting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final order = widget.order;
    final products = order.productDetails ?? [];
    final subtotal = products.fold<num>(0, (sum, p) => sum + (p.total ?? 0));
    const taxes = 0;
    final deliveryFee = order.deliveryDetails?.fare ?? order.fare ?? 0;
    final total = (order.paymentDetails?.amount ?? order.payment?.amount ?? 0);

    return Scaffold(
      appBar: const CustomAppbar(title: 'Receipt'),
      body: ListView(
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
          top: Sizer.height(16),
          bottom: Sizer.height(120),
        ),
        children: [
          RepaintBoundary(
            key: _receiptKey,
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.all(Sizer.width(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Image.asset(
                        AppImages.logoBlack,
                        height: Sizer.height(32),
                      ),
                      const Spacer(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Order #${order.orderId ?? ''}',
                            style: AppTypography.text16.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            AppUtils.dayWithSuffixMonthAndYear(
                                order.createdAt ?? DateTime.now()),
                            style: AppTypography.text12.copyWith(
                              color: AppColors.black70,
                            ),
                          ),
                          Text(
                            AppUtils.convertDateTime(
                                order.createdAt ?? DateTime.now()),
                            style: AppTypography.text12.copyWith(
                              color: AppColors.black70,
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                  const YBox(12),
                  const HLine(),
                  const YBox(12),
                  Text(
                    'Customer',
                    style: AppTypography.text14
                        .copyWith(fontWeight: FontWeight.w500),
                  ),
                  const YBox(8),
                  Text(
                    order.customerDetails?.name ??
                        '${order.firstname ?? ''} ${order.lastname ?? ''}'
                            .trim(),
                    style: AppTypography.text14,
                  ),
                  Text(order.customerDetails?.email ?? order.email ?? '',
                      style: AppTypography.text12
                          .copyWith(color: AppColors.black70)),
                  Text(order.customerDetails?.phone ?? order.phone ?? '',
                      style: AppTypography.text12
                          .copyWith(color: AppColors.black70)),
                  const YBox(12),
                  const HLine(),
                  const YBox(12),
                  Text(
                    'Items',
                    style: AppTypography.text14
                        .copyWith(fontWeight: FontWeight.w500),
                  ),
                  const YBox(8),
                  ...products.map((p) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                '${p.name ?? ''}${p.variation == null ? '' : ' (${p.variation})'} x${p.quantity ?? 1}',
                                style: AppTypography.text14,
                              ),
                            ),
                            Text(
                              '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: p.total ?? 0)}',
                              style: AppTypography.text14
                                  .copyWith(fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      )),
                  const YBox(12),
                  const HLine(),
                  const YBox(12),
                  Row(
                    children: [
                      Expanded(
                          child: Text('Subtotal',
                              style: AppTypography.text14
                                  .copyWith(color: AppColors.black70))),
                      Text(
                          '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: subtotal)}',
                          style: AppTypography.text14),
                    ],
                  ),
                  const YBox(8),
                  Row(
                    children: [
                      Expanded(
                          child: Text('Taxes',
                              style: AppTypography.text14
                                  .copyWith(color: AppColors.black70))),
                      Text(
                          '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: taxes)}',
                          style: AppTypography.text14),
                    ],
                  ),
                  const YBox(8),
                  Row(
                    children: [
                      Expanded(
                          child: Text('Delivery',
                              style: AppTypography.text14
                                  .copyWith(color: AppColors.black70))),
                      Text(
                          '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: deliveryFee)}',
                          style: AppTypography.text14),
                    ],
                  ),
                  const YBox(12),
                  CheckoutAmountTile(
                    leftText: 'Total',
                    rightText:
                        '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: total)}',
                    color: AppColors.primaryBlack,
                  ),
                  const YBox(12),
                  Text(
                    'Payment',
                    style: AppTypography.text14
                        .copyWith(fontWeight: FontWeight.w500),
                  ),
                  const YBox(4),
                  Text(
                    '${order.paymentDetails?.medium ?? order.payment?.medium ?? ''} • Ref: ${order.paymentDetails?.reference ?? order.payment?.reference ?? ''}',
                    style:
                        AppTypography.text12.copyWith(color: AppColors.black70),
                  ),
                  const YBox(16),
                  const HLine(),
                  const YBox(12),
                  Text(
                    'Thank you for shopping with Bottle King!',
                    style:
                        AppTypography.text12.copyWith(color: AppColors.black70),
                  ),
                ],
              ),
            ),
          ),
          const YBox(20),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(12)),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.grayF0),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _format,
                      items: const [
                        DropdownMenuItem(value: 'PNG', child: Text('PNG')),
                        DropdownMenuItem(value: 'PDF', child: Text('PDF')),
                      ],
                      onChanged: (v) => setState(() => _format = v ?? 'PNG'),
                    ),
                  ),
                ),
              ),
              const XBox(12),
              Expanded(
                child: CustomBtn.solid(
                  onTap: _isExporting ? null : _download,
                  online: true,
                  isLoading: _isExporting,
                  text: 'Download',
                ),
              ),
              const XBox(12),
              Expanded(
                child: CustomBtn.solid(
                  onTap: _isExporting ? null : _share,
                  online: true,
                  isLoading: _isExporting,
                  text: 'Share',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
