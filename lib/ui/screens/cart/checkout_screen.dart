import 'package:bottle_king_mobile/lib.dart';

class CheckoutScreen extends ConsumerStatefulWidget {
  const CheckoutScreen({
    super.key,
    required this.cartModel,
  });

  final CartModel cartModel;

  @override
  ConsumerState<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends ConsumerState<CheckoutScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final _phoneC = TextEditingController();
  final _codeC = TextEditingController();
  final _phoneF = FocusNode();
  final _couponF = FocusNode();

  /// Whether the user applied referral code
  bool couponOrReferralCodeIsVerified = false;
  bool isReferralCode = false;
  bool _isPhoneReadOnly = true;
  num? _discountValue;
  int selectedIndex = 0;

  PaymentMethodEnum selectedPaymentMethod = PaymentMethodEnum.paystack;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    KeyboardOverlay.addRemoveFocusNode(context, _phoneF);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _phoneC.text = ref.read(authVm).user?.phone ?? "";

      final addressRef = ref.read(addressVm);
      final cartRef = ref.read(cartVm);

      // Set the selected tab based on orderDeliveryType
      final orderDeliveryType = addressRef.orderDeliveryType;
      // Fetch shipping fee and reward tier in one shot
      if (orderDeliveryType == OrderDeliveryType.delivery) {
        final res = addressRef.isDeliveryInLagos
            ? await ref.read(addressVm.notifier).getShippingFee()
            : await addressRef.getOutsideLagosShippingFee(
                quantity: cartRef.totalCartQty);

        handleApiResponse(
          response: res,
          showSuccessMessage: false,
          errorMsg: "Unable to get shipping fee",
          onError: Navigator.of(context).pop,
        );
      }
      ref.read(orderViewModel).getRewardTier(widget.cartModel.total.toString());
      selectedIndex = orderDeliveryType == OrderDeliveryType.delivery ? 0 : 1;
      _tabController.animateTo(selectedIndex);

      await _autoFillReferralOrPromoCode();

      setState(() {});
    });
  }

  _autoFillReferralOrPromoCode() async {
    final refCode = await StorageService.getStringItem(StorageKey.referralCode);

    if (refCode != null) {
      _codeC.text = refCode;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneC.dispose();
    _codeC.dispose();
    _phoneF.dispose();
    _couponF.dispose();

    super.dispose();
  }

  bool get isFirstTimerUser => ref.watch(authVm).user?.isFirstTimeUser == true;
  bool get isFreeDeliveryWithinLagos =>
      ref.watch(appConfigVmodel).configData?.isFreeDeliveryWithinLagos == true;

  @override
  Widget build(BuildContext context) {
    final addressRef = ref.watch(addressVm);
    final authRef = ref.watch(authVm);
    final cartRef = ref.watch(cartVm);
    final couponRef = ref.watch(couponVmodel);

    printty("bbbbb ${ref.watch(addressVm).orderDeliveryType}");

    return BusyOverlay(
      show: ref.watch(orderViewModel).isBusy ||
          addressRef.isBusy ||
          ref.watch(paymentViewModel).isBusy,
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(100)),
          child: Container(
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: Sizer.height(20),
                          color: AppColors.black70,
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: Sizer.width(20)),
                      child: Text(
                        'Checkout',
                        style: AppTypography.text18.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container()
                  ],
                ),
                const YBox(30),
                Container(
                  height: Sizer.height(42),
                  width: Sizer.screenWidth,
                  decoration: BoxDecoration(
                    color: AppColors.greyF7,
                    // borderRadius: BorderRadius.circular(Sizer.radius(12)),
                    border: Border.all(width: 1, color: AppColors.grayF0),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(6),
                    vertical: Sizer.height(4),
                  ),
                  child: TabBar(
                    splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                    physics: const NeverScrollableScrollPhysics(),
                    onTap: (int value) {
                      setState(() {
                        selectedIndex = value;
                        ref.read(addressVm.notifier).setOrderDeliveryType(
                              selectedIndex == 0
                                  ? OrderDeliveryType.delivery
                                  : OrderDeliveryType.pickup,
                            );

                        if (selectedIndex == 0) {
                          ref.read(addressVm.notifier).getShippingFee();
                        }
                      });
                    },
                    dividerColor: Colors.transparent,
                    indicator: BoxDecoration(
                      borderRadius: BorderRadius.circular(Sizer.radius(8)),
                      color: AppColors.white,
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelColor: AppColors.primaryBlack,
                    automaticIndicatorColorAdjustment: true,
                    labelStyle: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    unselectedLabelStyle: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontWeight: FontWeight.w500,
                    ),
                    controller: _tabController,
                    tabs: const [
                      Tab(text: 'Delivery'),
                      Tab(text: 'Pick - up'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        body: ListView(
          padding: EdgeInsets.only(
            top: Sizer.height(20),
            bottom: Sizer.height(200),
          ),
          children: [
            if (ref.watch(orderViewModel).rewardTierModel != null &&
                ref.watch(appConfigVmodel).showPromo)
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(16),
                ),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: AppTypography.text12,
                    children: [
                      const TextSpan(
                        text: "Complete your order and earn ",
                      ),
                      TextSpan(
                        text:
                            "${ref.watch(orderViewModel).rewardTierModel?.tickets ?? ""}",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.yellow37,
                          fontWeight: FontWeight.w600,
                          fontFamily: "GeneralSans",
                        ),
                      ),
                      const TextSpan(
                        text: " raffle tickets and ",
                      ),
                      TextSpan(
                        text:
                            ref.watch(orderViewModel).rewardTierModel?.value ??
                                "",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.yellow37,
                          fontWeight: FontWeight.w600,
                          fontFamily: "GeneralSans",
                        ),
                      ),
                      const TextSpan(
                        text: " instant reward",
                      ),
                    ],
                  ),
                ),
              ),
            const YBox(24),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Deliver ${ref.watch(scheduleVmodel).isSchedule ? "on ${ref.watch(scheduleVmodel).formatedScheduleDate}" : "today"}",
                        style: AppTypography.text14.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: const ScheduleOrderModal(),
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(12),
                            vertical: Sizer.height(8),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.blackBD,
                            ),
                          ),
                          child: Text("Schedule",
                              style: AppTypography.text12.copyWith(
                                fontWeight: FontWeight.w600,
                              )),
                        ),
                      ),
                    ],
                  ),
                  const YBox(24),
                  const Divider(
                      color: AppColors.greyF7, thickness: 2, height: 1),
                  const YBox(16),
                  InkWell(
                    onTap: () async {
                      final r = await ModalWrapper.bottomSheet(
                        context: context,
                        widget: const AddressModal(fromCheckout: true),
                      );

                      if (r is OrderDeliveryArgs) {
                        final isDelivery =
                            r.deliveryType == OrderDeliveryType.delivery;
                        selectedIndex = isDelivery ? 0 : 1;
                        _tabController.animateTo(selectedIndex);
                        setState(() {});
                        if (isDelivery) {
                          // Fetch shipping fee and reward tier in one shot
                          final res = addressRef.isDeliveryInLagos
                              ? await ref
                                  .read(addressVm.notifier)
                                  .getShippingFee()
                              : await addressRef.getOutsideLagosShippingFee(
                                  quantity: cartRef.totalCartQty);

                          handleApiResponse(
                              response: res,
                              showSuccessMessage: false,
                              errorMsg: "Unable to get shipping fee",
                              onError: Navigator.of(context).pop,
                              successMsg: "Shipping fee updated",
                              onSuccess: () {
                                setState(() {});
                              });

                          // final res = await ref
                          //     .read(addressVm.notifier)
                          //     .getShippingFee(address: r.address);

                          // handleApiResponse(
                          //     response: res,
                          //     successMsg: "Shipping fee updated",
                          //     onSuccess: () {
                          //       ref.read(addressVm)
                          //         ..setDeliveryAddress(
                          //             r.address ?? const AddressArg())
                          //         ..setOrderDeliveryType(
                          //             OrderDeliveryType.delivery);
                          //       setState(() {});
                          //     });
                        }
                      }
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(AppSvgs.location),
                        const XBox(12),
                        Expanded(
                          child: Text(
                            addressRef.address,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const XBox(10),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(6),
                            vertical: Sizer.height(6),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.greyF7,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(20)),
                          ),
                          child: Icon(
                            Icons.keyboard_arrow_down,
                            size: Sizer.height(24),
                            color: AppColors.black70,
                          ),
                        )
                      ],
                    ),
                  ),
                  const YBox(16),
                  const Divider(
                      color: AppColors.greyF7, thickness: 2, height: 1),
                  // const YBox(16),
                  Row(
                    children: [
                      SvgPicture.asset(AppSvgs.call),
                      Expanded(
                        child: CustomTextField(
                          controller: _phoneC,
                          focusNode: _phoneF,
                          hideBorder: true,
                          hintText: "Add phone number",
                          isReadOnly: _isPhoneReadOnly,
                          keyboardType: KeyboardType.phone,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          _isPhoneReadOnly = !_isPhoneReadOnly;
                          if (!_isPhoneReadOnly) {
                            _phoneF.requestFocus();
                          }
                          setState(() {});
                        },
                        child: SvgPicture.asset(AppSvgs.pen),
                      )
                    ],
                  ),
                ],
              ),
            ),
            const YBox(16),
            Container(
              height: Sizer.height(4),
              width: Sizer.screenWidth,
              color: AppColors.greyF7,
            ),
            const YBox(30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Your items",
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(16),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    itemBuilder: (ctx, i) {
                      final p =
                          widget.cartModel.items?.regularProducts?.items?[i];
                      return OrderProductTile(
                        imageUrl: p?.image ?? "",
                        productName: p?.productName ?? "",
                        quantity: p?.quantity ?? 1,
                        price: p?.price ?? 0,
                      );
                    },
                    separatorBuilder: (_, __) => const YBox(6),
                    itemCount: widget
                            .cartModel.items?.regularProducts?.items?.length ??
                        0,
                  ),
                  const YBox(16),
                  Container(
                    height: Sizer.height(4),
                    width: Sizer.screenWidth,
                    color: AppColors.greyF7,
                  ),
                  const YBox(16),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "You have ",
                                    style: AppTypography.text16,
                                  ),
                                  TextSpan(
                                    text: AppUtils.formatNumber(
                                      decimalPlaces: 0,
                                      number: authRef.user?.points ?? 0,
                                    ),
                                    style: AppTypography.text16.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  TextSpan(
                                    text: " points! ",
                                    style: AppTypography.text16,
                                  ),
                                ],
                              ),
                            ),
                            const YBox(4),
                            Text(
                              "Redeem now for a discount!",
                              style: AppTypography.text16,
                            )
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          final r = await ref
                              .read(couponVmodel.notifier)
                              .rewardGenerateCoupon(authRef.user?.id ?? "");

                          if (r.success) {
                            _codeC.text = r.data?.code ?? "";
                          } else {
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message: r.message ?? "Something went wrong",
                            );
                          }
                        },
                        child: Container(
                          width: Sizer.width(90),
                          padding: EdgeInsets.symmetric(
                            vertical: Sizer.height(8),
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.blackBD,
                            ),
                          ),
                          child: cartRef.busy(rewardGenerateCouponState)
                              ? const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    LoaderIcon(size: 20),
                                  ],
                                )
                              : Center(
                                  child: Text(
                                    "Redeem",
                                    style: AppTypography.text14.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                        ),
                      )
                    ],
                  ),
                  const YBox(16),
                  CustomTextField(
                    controller: _codeC,
                    focusNode: _couponF,
                    hintText: "Enter referral or discount code",
                    borderRadius: 0,
                    suffixIcon: couponOrReferralCodeIsVerified
                        ? const Icon(
                            Icons.check_circle,
                            color: AppColors.green21,
                          )
                        : InkWell(
                            onTap: () async {
                              final code = _codeC.text.trim();
                              if (code.isEmpty) {
                                showWarningToast(
                                    "Please enter a valid referral or coupon code");
                                return;
                              }

                              final prefix = code.toUpperCase();
                              if (prefix.startsWith("BKR")) {
                                _validateReferralCode();
                              } else if (prefix.startsWith("BKC")) {
                                _validateCouponCode();
                              } else {
                                showWarningToast(
                                    "Invalid code. Use BKR... for referral or BKC... for coupon");
                                return;
                              }

                              // Romove referral code after use
                              StorageService.removeItem(
                                  StorageKey.referralCode);
                            },
                            child: Padding(
                              padding: EdgeInsets.only(
                                right: Sizer.width(16),
                                top: Sizer.height(12),
                              ),
                              child: couponRef.busy(validateCouponState)
                                  ? const LoaderIcon(size: 30)
                                  : Text(
                                      "Apply",
                                      style: AppTypography.text14.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          ),
                    onSubmitted: (p0) => _couponF.unfocus(),
                  ),
                  // Padding(
                  //   padding: EdgeInsets.only(
                  //     top: Sizer.width(4),
                  //   ),
                  //   child: Text(
                  //     "Referral discount is only for first time users",
                  //     style: AppTypography.text12.copyWith(
                  //       color: AppColors.red35,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
            const YBox(16),
            const HLine(),
            const YBox(16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Column(
                children: [
                  CheckoutAmountTile(
                    leftText: "Subtotal",
                    rightText:
                        '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.cartModel.total ?? 0)}',
                  ),
                  if (ref.watch(addressVm).orderDeliveryType ==
                      OrderDeliveryType.delivery) ...[
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const YBox(20),
                        if (ref.watch(addressVm).isDeliveryInLagos)
                          CheckoutAmountTile(
                            leftText: "Delivery fee",
                            rightTextColor: Colors.green,
                            rightText: isFreeDeliveryWithinLagos
                                ? "Free"
                                : '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: addressRef.deliveryFee?.cost ?? 0)}',
                          )
                        else
                          CheckoutAmountTile(
                            leftText: "Delivery (Outside Lagos)",
                            rightText:
                                '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: addressRef.deliveryFee?.cost ?? 0)}',
                          ),
                      ],
                    ),
                  ],
                  if (_discountValue != null && _discountValue! > 0)
                    const YBox(20),
                  if (_discountValue != null && _discountValue! > 0)
                    CheckoutAmountTile(
                      leftText: "Discount (${_discountValue ?? 0}%)",
                      rightText:
                          "-${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: (widget.cartModel.total ?? 0) * (_discountValue ?? 0) / 100)}",
                    ),
                  if (isReferralCode)
                    Padding(
                      padding: EdgeInsets.only(
                        top: Sizer.width(20),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CheckoutAmountTile(
                            leftText: "Referral discount",
                            rightText:
                                '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: referralDiscount)}',
                          ),
                          Text(
                            "(First time users only)",
                            style: AppTypography.text12.copyWith(
                              color: AppColors.gray75,
                            ),
                          ),
                        ],
                      ),
                    ),
                  const YBox(20),
                  CheckoutAmountTile(
                    leftText: "Order total",
                    rightText:
                        '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: orderTotal)}',
                    color: AppColors.primaryBlack,
                    rightTextColor: AppColors.primaryBlack,
                  ),
                  const YBox(20),
                ],
              ),
            ),
            const HLine(),
            const YBox(16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Payment method",
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(16),
                  const HLine(height: 2),
                  const YBox(16),
                  PaymentTile(
                    title: "Pay with Paystack",
                    isSelected:
                        selectedPaymentMethod == PaymentMethodEnum.paystack,
                    onSelect: () {
                      selectedPaymentMethod = PaymentMethodEnum.paystack;
                      setState(() {});
                    },
                  ),
                  const YBox(20),
                  // PaymentTile(
                  //   title: "Pay to Bottleking",
                  //   isSelected:
                  //       selectedPaymentMethod == PaymentMethodEnum.bottleking,
                  //   onSelect: () {
                  //     selectedPaymentMethod = PaymentMethodEnum.bottleking;
                  //     setState(() {});
                  //   },
                  // ),
                ],
              ),
            ),
          ],
        ),
        bottomSheet: Container(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
            top: Sizer.height(10),
            bottom: Sizer.height(30),
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                width: 1,
                color: AppColors.grayE6,
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomBtn.solid(
                onTap: () async {
                  if (selectedPaymentMethod == PaymentMethodEnum.paystack) {
                    _payWithPaystack();
                  } else if (selectedPaymentMethod ==
                      PaymentMethodEnum.bottleking) {
                    final r = await ref.read(paymentViewModel).payWithMecash(
                          amount: orderTotal,
                          orderId: widget.cartModel.id ?? "",
                        );
                    handleApiResponse(
                      response: r,
                      showErrorMessage: false,
                      onSuccess: () {
                        Navigator.pushNamed(
                            context, RoutePath.payWithBottlekingScreen);
                      },
                    );
                  }
                },
                online: true,
                text: "Place order",
              ),
              const YBox(10),
              CustomBtn.solid(
                onTap: () {
                  _showDeleteConfirmationModal();
                },
                online: true,
                isOutline: true,
                outlineColor: AppColors.blackBD,
                textColor: AppColors.red15,
                text: "Clear order",
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmationModal() {
    final loadingProvider = StateProvider<bool>((ref) => false);
    ModalWrapper.bottomSheet(
      context: context,
      widget: Consumer(
        builder: (context, ref, child) {
          final isLoading = ref.watch(loadingProvider);
          return ConfirmModal(
            title: "Clear order",
            subtitle: "Are you sure you want to clear your order?",
            rightBtnText: "Clear",
            isLoading: isLoading,
            rightBtnTap: () async {
              ref.read(loadingProvider.notifier).state = true;

              try {
                final r = await ref
                    .read(cartVm.notifier)
                    .clearCartOrWishlist(isWishList: false);
                handleApiResponse(
                  response: r,
                  onSuccess: () {
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      RoutePath.bottomNavScreen,
                      (r) => false,
                      arguments: DashArg(index: 3),
                    );
                  },
                );
              } finally {
                if (context.mounted) {
                  ref.read(loadingProvider.notifier).state = false;
                }
              }
            },
          );
        },
      ),
    );
  }

  Future<ApiResponse<dynamic>> _initiateCheckout() async {
    final addressRef = ref.read(addressVm);
    final authRef = ref.read(authVm);
    final scheduleVm = ref.read(scheduleVmodel);
    return ref.read(orderViewModel.notifier).initiateCheckout(args: {
      "cartId": widget.cartModel.id,
      "customerId": authRef.user?.id,
      "deliveryFare": isFreeDeliveryWithinLagos
          ? 0
          : ref.read(addressVm).isDelivery
              ? ref.read(addressVm).deliveryFee?.cost ?? 0
              : 0,
      "address": ref.read(addressVm).address,
      if (addressRef.orderDeliveryType == OrderDeliveryType.delivery)
        "address_lng": ref.read(addressVm).deliveryAddress?.lng ?? "",
      if (addressRef.orderDeliveryType == OrderDeliveryType.delivery)
        "address_lat": ref.read(addressVm).deliveryAddress?.lat ?? "",
      "couponCode": (couponOrReferralCodeIsVerified && !isReferralCode)
          ? _codeC.text.trim()
          : null,
      "date": scheduleVm.scheculeDate?.toIso8601String(),
      "deliveryType": ref.read(addressVm).isDelivery ? "drop-off" : "pick-up",
      "deliveryTime": scheduleVm.scheduleTimeString,
      "addPaper": false,
      "medium": "Mobile App", // ,
      "referralCode":
          (isFirstTimerUser && isReferralCode) ? _codeC.text.trim() : null,
    });
  }

  _payWithPaystack() async {
    FocusScope.of(context).unfocus();
    final authRef = ref.read(authVm);
    final scheduleVm = ref.read(scheduleVmodel);
    final r = await _initiateCheckout();

    printty("initiateCheckout ${r.data}");
    if (r.success) {
      final r = await ref.read(paymentViewModel.notifier).initiateTransaction(
            email: authRef.user?.email ?? "",
            amount: orderTotal.toDouble() * 100,
            deliveryType:
                ref.read(addressVm).isDelivery ? "drop-off" : "pick-up",
            cartId: widget.cartModel.id ?? "",
            referralCode: (isFirstTimerUser && isReferralCode)
                ? _codeC.text.trim()
                : null,
          );
      if (r.success) {
        if (mounted) {
          // Reset schedule delivery type
          scheduleVm.setScheduleDeliveryType(DeliveryType.now);
          await AnalyticsService.instance.trackCheckoutStarted(
            cartId: widget.cartModel.id ?? '',
            total: orderTotal,
          );
          Navigator.pushNamed(
            context,
            RoutePath.customWebViewScreen,
            arguments: WebViewArg(
              webURL: r.data["authorization_url"],
              onSucecess: () async {
                await Future.delayed(const Duration(seconds: 4));
                if (mounted) {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    RoutePath.bottomNavScreen,
                    (_) => false,
                    arguments: DashArg(index: 2),
                  );

                  await AnalyticsService.instance.trackOrderCompleted(
                    orderId: r.data["reference"],
                    total: orderTotal,
                  );
                  Navigator.pushNamed(context, RoutePath.orderDetailsScreen,
                      arguments: OrderDetailsArg(ref: r.data["reference"]));
                  // if (createdOrder.success && createdOrder.data is OrderModel) {
                  //   printty("create order cred: ${createdOrder.data}");

                  // }
                }
              },
            ),
          );
        }
      } else {
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message: r.message ?? "Something went wrong",
        );
      }
    } else {
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: r.message ?? "Something went wrong",
      );
    }
  }

  // 5% referral discount is only for first time users
  double get referralDiscount {
    final subTotal = widget.cartModel.total?.toDouble() ?? 0;
    return isReferralCode ? subTotal * 0.05 : 0;
  }

  double get orderTotal {
    final addVm = ref.watch(addressVm);
    final isDelivery = addVm.orderDeliveryType == OrderDeliveryType.delivery;

    final subTotal = widget.cartModel.total ?? 0;

    return subTotal +
        (isDelivery ? deliveryFee : 0) -
        discountAmt -
        (isFirstTimerUser ? referralDiscount : 0);
  }

  // Calculate delivery fee based on location and free delivery config
  double get deliveryFee {
    bool isWithinLagos = ref.watch(addressVm).isDeliveryInLagos;

    return !isWithinLagos
        ? (ref.watch(addressVm).deliveryFee?.cost ?? 0).toDouble()
        : isFreeDeliveryWithinLagos
            ? 0
            : (ref.watch(addressVm).deliveryFee?.cost ?? 0).toDouble();
  }

  double get discountAmt {
    return (widget.cartModel.total ?? 0) * (_discountValue ?? 0) / 100;
  }

  _validateCouponCode() async {
    isReferralCode = false;
    final r = await ref
        .read(couponVmodel.notifier)
        .validateCoupon(_codeC.text.trim());

    handleApiResponse(
        response: r,
        onSuccess: () {
          _discountValue = r.data ?? 0;
          couponOrReferralCodeIsVerified = true;
        },
        onError: () {
          _discountValue = 0;
          couponOrReferralCodeIsVerified = false;
        });

    setState(() {});
    // if (r.success) {
    //   _discountValue = r.data ?? 0;
    //   couponOrReferralCodeIsVerified = true;
    //   setState(() {});
    // } else {
    //   _discountValue = 0;
    //   couponOrReferralCodeIsVerified = false;
    //   setState(() {});
    // }
  }

  Future<void> _validateReferralCode() async {
    final r = await ref
        .read(couponVmodel.notifier)
        .verifyReferralCode(_codeC.text.trim());

    handleApiResponse(
        response: r,
        onSuccess: () {
          isReferralCode = true;
          couponOrReferralCodeIsVerified = true;
        },
        onError: () {
          isReferralCode = false;
          couponOrReferralCodeIsVerified = false;
        });

    // if (r.success) {
    //   isReferralCode = true;
    //   couponOrReferralCodeIsVerified = true;
    // } else {
    //   isReferralCode = false;
    //   couponOrReferralCodeIsVerified = false;
    // }

    setState(() {});
  }
}

class PaymentTile extends StatelessWidget {
  const PaymentTile({
    super.key,
    required this.title,
    this.isSelected = false,
    this.onSelect,
  });

  final String title;
  final bool isSelected;
  final Function()? onSelect;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onSelect,
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.black70,
              ),
            ),
          ),
          CustomRadioBtn(
            isSelected: isSelected,
          )
        ],
      ),
    );
  }
}
