import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class PayWithBottlekingScreen extends ConsumerStatefulWidget {
  const PayWithBottlekingScreen({super.key});

  @override
  ConsumerState<PayWithBottlekingScreen> createState() =>
      _PayWithBottlekingScreenState();
}

class _PayWithBottlekingScreenState
    extends ConsumerState<PayWithBottlekingScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "",
      ),
      body: Column(
        children: [
          const YBox(20),
          Container(
            width: Sizer.screenWidth,
            padding: EdgeInsets.all(Sizer.radius(24)),
            margin: EdgeInsets.symmetric(horizontal: Sizer.radius(16)),
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.grayE6,
                width: 1.0,
              ),
            ),
            child: Column(
              children: [
                Text(
                  "Transfer NGN 5,000 to the account below",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const YBox(20),
                Container(
                  width: Sizer.screenWidth,
                  padding: EdgeInsets.all(Sizer.radius(20)),
                  decoration: const BoxDecoration(
                    color: AppColors.grayF8,
                  ),
                  child: Column(
                    children: [
                      const PayListTile(
                        title: "BANK NAME",
                        subTitle: "Mecash",
                      ),
                      const YBox(16),
                      const HLine(color: AppColors.grayE6, height: 1),
                      const YBox(16),
                      PayListTile(
                        title: "ACCOUNT NUMBER",
                        subTitle: "*********",
                        onCopy: () {},
                      ),
                      const YBox(16),
                      const HLine(color: AppColors.grayE6, height: 1),
                      const YBox(16),
                      const PayListTile(
                        title: "ACCOUNT NAME",
                        subTitle: "Ayodeji",
                      ),
                    ],
                  ),
                ),
                const YBox(40),
                CustomBtn.solid(
                  onTap: () async {},
                  online: true,
                  text: "I’ve sent the money",
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class PayListTile extends StatelessWidget {
  const PayListTile({
    super.key,
    required this.title,
    required this.subTitle,
    this.onCopy,
  });

  final String title;
  final String subTitle;
  final Function()? onCopy;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray90,
                ),
              ),
              const YBox(8),
              Text(
                subTitle,
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        if (onCopy != null) ...[
          GestureDetector(
            onTap: onCopy,
            child: SvgPicture.asset(
              AppSvgs.copy,
              height: Sizer.height(20),
            ),
          ),
        ],
      ],
    );
  }
}
