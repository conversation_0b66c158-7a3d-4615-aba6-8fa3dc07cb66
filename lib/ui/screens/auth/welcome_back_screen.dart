import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flutter/gestures.dart';

class WelcomeBackScreen extends ConsumerStatefulWidget {
  const WelcomeBackScreen({super.key});

  @override
  ConsumerState<WelcomeBackScreen> createState() => _WelcomeBackScreenState();
}

class _WelcomeBackScreenState extends ConsumerState<WelcomeBackScreen>
    with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;
  Country country = Country(
    e164Sc: 0,
    geographic: true,
    level: 2,
    example: "Nigeria",
    name: "Nigeria",
    countryCode: "NG",
    phoneCode: "234",
    displayName: "Nigeria",
    displayNameNoCountryCode: "Nigeria",
    e164Key: "234-NG-0",
  );
  final phoneC = TextEditingController();
  final phoneF = FocusNode();
  bool _phoneValid = false;
  String? _phoneError;
  bool _checking = false;

  @override
  void initState() {
    super.initState();
    phoneF.requestFocus();

    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
  }

  @override
  void dispose() {
    phoneC.dispose();
    phoneF.dispose();
    _customController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: CustomAppbar(
          title: "",
          onBack: () {
            // check if it can pop
            if (ModalRoute.of(context)?.canPop == true) {
              Navigator.pop(context);
            }
          },
        ),
        body: FadeTransition(
          opacity: _customAnimation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.2),
              end: const Offset(0, 0),
            ).animate(_customAnimation),
            child: ListView(
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
              ),
              children: [
                imageHelper(
                  AppImages.logoBlack,
                  height: Sizer.height(48),
                ),
                const YBox(30),
                Text(
                  "Welcome back",
                  textAlign: TextAlign.center,
                  style: AppTypography.text24.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(4),
                Text(
                  "Login to your account to continue shopping your \nfavourite drinks",
                  textAlign: TextAlign.center,
                  style: AppTypography.text14.copyWith(
                    color: AppColors.black70,
                  ),
                ),
                const YBox(40),
                CustomTextField(
                  controller: phoneC,
                  focusNode: phoneF,
                  hintText: "enter phone number",
                  labelText: "Phone number",
                  showLabelHeader: true,
                  borderRadius: 0,
                  keyboardType: KeyboardType.phone,
                  errorText: phoneF.hasFocus ? _phoneError : null,
                  onChanged: (value) {
                    setState(() {
                      _checking = true;
                    });
                    final res = PhoneUtils.validateLocal(
                      isoCountryCode: country.countryCode,
                      rawLocalInput: value,
                    );
                    setState(() {
                      _phoneValid = res.isValid;
                      _phoneError = res.errorMessage;
                      _checking = false;
                    });
                    authRef.reBuildUI();
                  },
                  // showSuffixIcon: true,
                  suffixIcon: _checking
                      ? const LoaderIcon(size: 20)
                      : (_phoneValid
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : (phoneF.hasFocus && (phoneC.text.isNotEmpty))
                              ? const Icon(Icons.error, color: Colors.red)
                              : null),
                  prefixIcon: InkWell(
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        countryListTheme: CountryListThemeData(
                          flagSize: 25,
                          textStyle: TextStyle(
                            fontSize: Sizer.text(16),
                            color: Colors.blueGrey,
                          ),
                          bottomSheetHeight: Sizer.screenHeight * 0.7,

                          //Optional. Sets the border radius for the bottomsheet.
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20.0),
                            topRight: Radius.circular(20.0),
                          ),
                          //Optional. Styles the search field.
                          inputDecoration: InputDecoration(
                            labelText: 'Search',
                            hintText: 'Start typing to search',
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: const Color(0xFF8C98A8).withOpacity(0.2),
                              ),
                            ),
                          ),
                        ),
                        onSelect: (Country selectedCountry) {
                          setState(() {
                            country = selectedCountry;
                          });
                        },
                      );
                    },
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: Sizer.width(10)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            country.flagEmoji,
                            style: AppTypography.text20.copyWith(),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            country.countryCode,
                            style: AppTypography.text16.copyWith(),
                          ),
                          Icon(
                            Icons.keyboard_arrow_down,
                            size: Sizer.height(24),
                            color: AppColors.black70,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                const YBox(24),
                CustomBtn.solid(
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final r = await authRef.login(
                        args: AuthArg(
                      username: PhoneUtils.formatInternationalDigits(
                        countryPhoneCode: country.phoneCode,
                        rawLocalInput: phoneC.text.trim(),
                      ),
                    ));
                    handleApiResponse(
                      response: r,
                      onSuccess: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.otpScreen,
                          arguments: OtpArg(
                              phoneNo: PhoneUtils.formatE164(
                            countryPhoneCode: country.phoneCode,
                            rawLocalInput: phoneC.text.trim(),
                          )),
                        );
                      },
                    );
                  },
                  online: _phoneValid,
                  text: "Continue",
                ),
                const YBox(16),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Don’t have an account? ",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.black70,
                          fontFamily: 'GeneralSans',
                        ),
                      ),
                      TextSpan(
                        text: " Sign up",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.primaryBlack,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'GeneralSans',
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(
                                context, RoutePath.registerScreen);
                          },
                      ),
                    ],
                  ),
                ),
                const YBox(40),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: Sizer.height(0.6),
                        width: double.infinity,
                        color: AppColors.grayEEE,
                      ),
                    ),
                    const XBox(8),
                    Text(
                      "OR",
                      textAlign: TextAlign.center,
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const XBox(8),
                    Expanded(
                      child: Container(
                        height: Sizer.height(0.6),
                        width: double.infinity,
                        color: AppColors.grayEEE,
                      ),
                    ),
                  ],
                ),
                const YBox(32),
                CustomBtn.solid(
                  text: "Sign in with email",
                  isOutline: true,
                  outlineColor: AppColors.grayDD,
                  textColor: AppColors.gray54,
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    Navigator.pushNamed(context, RoutePath.loginScreen);
                  },
                ),
                const YBox(12),
                CustomBtn.solid(
                  text: "Continue as guest",
                  isOutline: true,
                  outlineColor: AppColors.grayDD,
                  textColor: AppColors.gray54,
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    Navigator.pushReplacementNamed(
                        context, RoutePath.bottomNavScreen);
                  },
                ),
                const YBox(40),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
