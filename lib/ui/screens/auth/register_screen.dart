import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flutter/gestures.dart';

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen>
    with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;
  Country country = Country(
    e164Sc: 0,
    geographic: true,
    level: 2,
    example: "Nigeria",
    name: "Nigeria",
    countryCode: "NG",
    phoneCode: "234",
    displayName: "Nigeria",
    displayNameNoCountryCode: "Nigeria",
    e164Key: "234-NG-0",
  );
  final _fullNameC = TextEditingController();
  final _emailC = TextEditingController();
  final _passwordC = TextEditingController();
  final _phoneC = TextEditingController();
  final _dobC = TextEditingController();

  final _emailF = FocusNode();

  DateTime? _selectedDOB;
  bool _isAgreed = false;
  bool _phoneValid = false;
  String? _phoneError;
  bool _checking = false;

  @override
  void initState() {
    super.initState();

    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
  }

  @override
  void dispose() {
    _fullNameC.dispose();
    _emailC.dispose();
    _passwordC.dispose();
    _phoneC.dispose();
    _dobC.dispose();
    _emailF.dispose();
    _customController.dispose();

    super.dispose();
  }

  bool get btnIsActive {
    return _fullNameC.text.isNotEmpty &&
        _emailC.text.isNotEmpty &&
        _phoneValid &&
        _isAgreed;
    // _selectedDOB != null;
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "",
        ),
        body: FadeTransition(
          opacity: _customAnimation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.2),
              end: const Offset(0, 0),
            ).animate(_customAnimation),
            child: ListView(
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
              ),
              children: [
                imageHelper(
                  AppImages.logoBlack,
                  height: Sizer.height(48),
                ),
                const YBox(24),
                CustomTextField(
                  controller: _fullNameC,
                  labelText: "Full name",
                  showLabelHeader: true,
                  borderRadius: 0,
                  onChanged: (p0) => setState(() {}),
                ),
                const YBox(16),
                CustomTextField(
                  controller: _emailC,
                  focusNode: _emailF,
                  labelText: "Email",
                  keyboardType: KeyboardType.email,
                  showLabelHeader: true,
                  borderRadius: 0,
                  errorText: _emailF.hasFocus &&
                          (!_emailC.text.trim().contains(".") ||
                              !_emailC.text.trim().contains("@"))
                      ? "Please enter a valid email"
                      : null,
                  onChanged: (p0) => setState(() {}),
                ),
                const YBox(16),
                CustomTextField(
                  controller: _phoneC,
                  hintText: "enter phone number",
                  labelText: "Phone number",
                  showLabelHeader: true,
                  borderRadius: 0,
                  keyboardType: KeyboardType.phone,
                  errorText: _phoneError,
                  onChanged: (value) {
                    setState(() {
                      _checking = true;
                    });
                    final res = PhoneUtils.validateLocal(
                      isoCountryCode: country.countryCode,
                      rawLocalInput: value,
                    );
                    setState(() {
                      _phoneValid = res.isValid;
                      _phoneError = res.errorMessage;
                      _checking = false;
                    });
                  },
                  suffixIcon: _checking
                      ? const LoaderIcon(size: 20)
                      : (_phoneValid
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : (_phoneC.text.isNotEmpty)
                              ? const Icon(Icons.error, color: Colors.red)
                              : null),
                  prefixIcon: InkWell(
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        countryListTheme: CountryListThemeData(
                          flagSize: 25,
                          textStyle: TextStyle(
                            fontSize: Sizer.text(16),
                            color: Colors.blueGrey,
                          ),
                          bottomSheetHeight: Sizer.screenHeight * 0.7,

                          //Optional. Sets the border radius for the bottomsheet.
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20.0),
                            topRight: Radius.circular(20.0),
                          ),
                          //Optional. Styles the search field.
                          inputDecoration: InputDecoration(
                            labelText: 'Search',
                            hintText: 'Start typing to search',
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: const Color(0xFF8C98A8).withOpacity(0.2),
                              ),
                            ),
                          ),
                        ),
                        onSelect: (Country selectedCountry) {
                          setState(() {
                            country = selectedCountry;
                          });
                        },
                      );
                    },
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: Sizer.width(10)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            country.flagEmoji,
                            style: AppTypography.text20.copyWith(),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            country.countryCode,
                            style: AppTypography.text16.copyWith(),
                          ),
                          Icon(
                            Icons.keyboard_arrow_down,
                            size: Sizer.height(24),
                            color: AppColors.black70,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                const YBox(16),
                // CustomTextField(
                //   controller: _passwordC,
                //   focusNode: _passwordF,
                //   labelText: "Password",
                //   keyboardType: KeyboardType.email,
                //   showLabelHeader: true,
                //   borderRadius: 0,
                //   isPassword: true,
                //   onChanged: (p0) => setState(() {}),
                // ),
                // const YBox(16),
                CustomTextField(
                  controller: _dobC,
                  hintText: "Select date",
                  labelText: "Date of birth",
                  optionalText: "(optional)",
                  showLabelHeader: true,
                  isReadOnly: true,
                  borderRadius: 0,
                  onChanged: (p0) => setState(() {}),
                  suffixIcon: Icon(
                    Iconsax.calendar_1,
                    size: Sizer.height(20),
                    color: AppColors.black70,
                  ),
                  onTap: () {
                    CustomCupertinoDatePicker(
                      context: context,
                      initialDateTime: _selectedDOB ??
                          DateTime.now().subtract(const Duration(days: 6570)),
                      maximumDate:
                          DateTime.now().subtract(const Duration(days: 6570)),
                      onDateTimeChanged: (dateTime) {
                        _selectedDOB = dateTime;
                      },
                      onDone: () {
                        // Validate age with proper calculation using AppUtils
                        if (_selectedDOB != null) {
                          if (!AppUtils.isValidAge(_selectedDOB!)) {
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message:
                                  "You must be 18 years and above to register",
                            );
                            _selectedDOB = null;
                            setState(() {});
                          } else {
                            _dobC.text = AppUtils.dayWithSuffixMonthAndYear(
                                _selectedDOB ?? DateTime.now());
                            setState(() {});
                          }
                        }
                        Navigator.pop(context);
                      },
                    ).show();
                  },
                ),
                const YBox(24),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomCheckbox(
                      isSelected: _isAgreed,
                      onTap: () {
                        _isAgreed = !_isAgreed;
                        setState(() {});
                      },
                    ),
                    const XBox(8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "I can confirm that I am 18+",
                            style: AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'GeneralSans',
                            ),
                          ),
                          RichText(
                            textAlign: TextAlign.left,
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: "By continuing, you agree to our ",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.black70,
                                    fontFamily: 'GeneralSans',
                                  ),
                                ),
                                TextSpan(
                                  text: "Terms of Use",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.primaryBlack,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'GeneralSans',
                                  ),
                                ),
                                TextSpan(
                                  text:
                                      " and acknowlegde that you have read our ",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.black70,
                                    fontFamily: 'GeneralSans',
                                  ),
                                ),
                                TextSpan(
                                  text: "Privacy Policy",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.primaryBlack,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'GeneralSans',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const YBox(24),
                CustomBtn.solid(
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final r = await ref.read(authVm.notifier).createAccount(
                          firstName: _fullNameC.text.trim(),
                          lastName: _fullNameC.text.trim(),
                          email: _emailC.text.trim(),
                          phone: PhoneUtils.formatInternationalDigits(
                            countryPhoneCode: country.phoneCode,
                            rawLocalInput: _phoneC.text.trim(),
                          ),
                          password: _passwordC.text.trim(),
                          dob: _selectedDOB?.toIso8601String() ?? "",
                        );

                    handleApiResponse(
                      response: r,
                      onSuccess: () {
                        // Navigator.pushNamed(
                        //   context,
                        //   RoutePath.otpScreen,
                        //   arguments: OtpArg(
                        //     phoneNo: PhoneUtils.formatE164(
                        //       countryPhoneCode: country.phoneCode,
                        //       rawLocalInput: _phoneC.text.trim(),
                        //     ),
                        //   ),
                        // );
                        Navigator.pushNamed(context, RoutePath.bottomNavScreen);
                      },
                    );
                  },
                  online: btnIsActive,
                  text: "Continue",
                ),
                // const YBox(24),
                // Text(
                //   "OR",
                //   textAlign: TextAlign.center,
                //   style: AppTypography.text14.copyWith(
                //     color: AppColors.black70,
                //     fontWeight: FontWeight.w600,
                //   ),
                // ),
                // const YBox(24),
                // SocialBtn(
                //   iconPath: AppSvgs.google,
                //   btnText: "Sign up with Google",
                //   onTap: () {},
                // ),
                // const YBox(12),
                // SocialBtn(
                //   iconPath: AppSvgs.apple,
                //   btnText: "Sign up with Apple",
                //   onTap: () {},
                // ),
                const YBox(20),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Already have an account? ",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.black70,
                          fontFamily: 'GeneralSans',
                        ),
                      ),
                      TextSpan(
                        text: " Login",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.primaryBlack,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'GeneralSans',
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pop(context);
                          },
                      ),
                    ],
                  ),
                ),
                const YBox(80),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
