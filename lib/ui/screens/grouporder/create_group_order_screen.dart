import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:bottle_king_mobile/ui/components/shared/textfields/invite_chip_field.dart';

class CreateGroupOrderScreen extends ConsumerStatefulWidget {
  const CreateGroupOrderScreen({super.key});

  @override
  ConsumerState<CreateGroupOrderScreen> createState() =>
      _CreateGroupOrderScreenState();
}

class _CreateGroupOrderScreenState
    extends ConsumerState<CreateGroupOrderScreen> {
  final nameC = TextEditingController();
  final orderTimelineC = TextEditingController();
  final paymentMethodC = TextEditingController();

  final nameF = FocusNode();

  List<String> _members = [];
  DateTime? _selectedDOB;

  @override
  void dispose() {
    nameC.dispose();
    orderTimelineC.dispose();
    paymentMethodC.dispose();
    nameF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final groupOrderVm = ref.watch(groupOrderVmodel);
    return BusyOverlay(
      show: groupOrderVm.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "Create group order",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            const YBox(50),
            CustomTextField(
              controller: nameC,
              focusNode: nameF,
              labelText: "Group order name ",
              showLabelHeader: true,
              borderRadius: 0,
            ),
            const YBox(16),
            CustomTextField(
              controller: orderTimelineC,
              labelText: "Set order timeline",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              suffixIcon: Icon(
                Iconsax.calendar_1,
                size: Sizer.height(20),
                color: AppColors.black70,
              ),
              onChanged: (p0) => setState(() {}),
              onTap: () {
                CustomCupertinoDatePicker(
                  context: context,
                  // initialDateTime: DateTime.now(),
                  minimumDate: DateTime.now(),
                  onDateTimeChanged: (dateTime) {
                    _selectedDOB = dateTime;
                  },
                  onDone: () {
                    orderTimelineC.text = AppUtils.dayWithSuffixMonthAndYear(
                        _selectedDOB ?? DateTime.now());
                    Navigator.pop(context);
                  },
                ).show();
              },
            ),
            const YBox(16),
            CustomTextField(
              controller: paymentMethodC,
              labelText: "Choose payment method",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              isReadOnly: true,
              onTap: () {},
            ),
            const YBox(16),
            InviteChipField(
              labelText: 'Invite by phone number or email',
              // max: 10,
              onInviteesChanged: (invitees) {
                _members = invitees;
                setState(() {});
              },
            ),
            // CustomTextField(
            //   labelText: "Add members",
            //   keyboardType: KeyboardType.email,
            //   showLabelHeader: true,
            //   borderRadius: 0,
            //   onChanged: (p0) => setState(() {}),
            // ),
            const YBox(30),
            CustomBtn.solid(
              onTap: () async {
                FocusScope.of(context).unfocus();

                List<Participant> participants = _members.map((e) {
                  return Participant(email: e);
                }).toList();
                final res = await ref.read(groupOrderVmodel).createGroupOrder(
                      GroupOrderParams(
                        name: nameC.text,
                        deadline: _selectedDOB,
                        paymentMethod: "host_pays_all",
                        participants: participants,
                      ),
                    );

                handleApiResponse(
                    response: res,
                    onSuccess: () {
                      Navigator.pop(context);
                    });
              },
              text: "Continue",
            ),
          ],
        ),
      ),
    );
  }
}
