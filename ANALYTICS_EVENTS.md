# Mixpanel Analytics Events

## Initialization
- Uses `MIXPANEL_PROJECT_TOKEN` from `.env.<flavor>` via `EnvConfig.mixpanelProjectToken`.
- Initializes during app startup in `lib/core/services/app_init_service.dart:29`.

## Privacy and Opt-Out
- Opt-out persisted in `StorageKey.analyticsOptOut`.
- Toggle via `AnalyticsService.instance.setTrackingEnabled(bool)`.
- When opted out, no events are sent.

## User Identity
- Identified with `AuthUserModel.id` using `identifyUser`.
- People properties set: `email`, `firstname`, `lastname`, `phone`, `role`, `verified`.

## Super Properties
- Register global properties with `registerSuperProperties`.

## Event Naming
- Lower snake_case.

## Events
- `login_success`
  - Properties: `method: string`
- `logout`
  - Properties: none
- `product_view`
  - Properties: `product_id: string`, `name: string?`
- `add_to_cart`
  - Properties: `product_id: string`, `variation_id: string?`, `quantity: int`
- `checkout_started`
  - Properties: `cart_id: string`, `total: number?`
- `order_completed`
  - Properties: `order_id: string`, `total: number?`

## Testing Procedures
- Unit tests in `test/mixpanel_service_test.dart` validate API calls via a fake client.
- Manual verification:
  - Run app and perform key actions.
  - Use Mixpanel Live View to confirm events.
  - Call `AnalyticsService.instance.flush()` to force delivery.

## Platform Notes
- Works on Android and iOS with `mixpanel_flutter`.
-