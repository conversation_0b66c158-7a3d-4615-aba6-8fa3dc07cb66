PODS:
  - appsflyer_sdk (6.17.7):
    - appsflyer_sdk/Core (= 6.17.7)
  - appsflyer_sdk/Core (6.17.7):
    - AppsFlyerFramework (= 6.17.7)
    - Flutter
  - AppsFlyerFramework (6.17.7):
    - AppsFlyerFramework/Main (= 6.17.7)
  - AppsFlyerFramework/Main (6.17.7)
  - Flutter (1.0.0)
  - gal (1.0.0):
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - Mixpanel-swift (5.1.0):
    - Mixpanel-swift/Complete (= 5.1.0)
  - Mixpanel-swift/Complete (5.1.0)
  - mixpanel_flutter (2.4.4):
    - Flutter
    - Mixpanel-swift (= 5.1.0)
  - store_redirect (0.0.1):
    - Flutter

DEPENDENCIES:
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - Flutter (from `Flutter`)
  - gal (from `.symlinks/plugins/gal/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - mixpanel_flutter (from `.symlinks/plugins/mixpanel_flutter/ios`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)

SPEC REPOS:
  trunk:
    - AppsFlyerFramework
    - Mixpanel-swift

EXTERNAL SOURCES:
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  Flutter:
    :path: Flutter
  gal:
    :path: ".symlinks/plugins/gal/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  mixpanel_flutter:
    :path: ".symlinks/plugins/mixpanel_flutter/ios"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"

SPEC CHECKSUMS:
  appsflyer_sdk: a6fdf00590a89e91bd9794815691ab8738e465b9
  AppsFlyerFramework: ae7caf1688348f2d7a742037bd4fc6966d39d362
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  gal: a7198871e0d63bdffb906fbbcc9e16b58031a357
  geocoding_ios: d7460f56e80e118d57678efe5c2cdc888739ff18
  Mixpanel-swift: 7b26468fc0e2e521104e51d65c4bbf7cab8162f8
  mixpanel_flutter: c2bb8345c90bef15512a1b812ec800b52f8614b6
  store_redirect: 2977747cf81689a39bd62c248c2deacb7a0d131e

PODFILE CHECKSUM: b3a1692cb3bfbea300154ce2227e80b63533e718

COCOAPODS: 1.16.2
